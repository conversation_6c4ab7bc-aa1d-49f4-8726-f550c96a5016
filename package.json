{"name": "<PERSON><PERSON>-writer", "version": "0.1.0", "description": "Editor de texto avanzado para escritores con estructura jerárquica flexible", "main": ".electron/main.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "electron:dev": "concurrently \"npm run dev\" \"electron .\"", "electron:build": "npm run build && electron-builder", "lint": "eslint src --ext ts,tsx", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.0.2", "@tiptap/extension-character-count": "^2.14.0", "@tiptap/extension-link": "^2.24.1", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-text-align": "^2.24.1", "@tiptap/extension-underline": "^2.24.1", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "electron-store": "^8.1.0", "lucide-react": "^0.309.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-resizable-panels": "^1.0.9", "sqlite3": "^5.1.7", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.1.3", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.0.11"}, "author": "", "license": "ISC"}