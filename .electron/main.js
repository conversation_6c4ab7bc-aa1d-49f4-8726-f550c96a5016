const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();

// Mantener una referencia global del objeto window
let mainWindow;

function createWindow() {
  // Crear la ventana del navegador
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Cargar la aplicación
  const startUrl = process.env.ELECTRON_START_URL || 
    `file://${path.join(__dirname, '../dist/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Abrir DevTools en desarrollo
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Cuando la ventana se cierre
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

// Crear ventana cuando la app esté lista
app.whenReady().then(createWindow);

// Salir cuando todas las ventanas estén cerradas
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function () {
  if (mainWindow === null) createWindow();
});

// Gestión de proyectos
ipcMain.handle('create-project', async (event, projectName, projectPath) => {
  try {
    // Crear directorio del proyecto si no existe
    if (!fs.existsSync(projectPath)) {
      fs.mkdirSync(projectPath, { recursive: true });
    }

    const dbPath = path.join(projectPath, 'project.db');
    const db = new sqlite3.Database(dbPath);

    // Crear tablas
    db.serialize(() => {
      // Tabla Documents
      db.run(`CREATE TABLE IF NOT EXISTS Documents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        synopsis TEXT,
        order_index INTEGER,
        parent_id INTEGER,
        is_folder BOOLEAN,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        include_in_compile BOOLEAN DEFAULT 1,
        compile_order INTEGER,
        compile_content BOOLEAN DEFAULT 1,
        compile_title BOOLEAN DEFAULT 1,
        compile_synopsis BOOLEAN DEFAULT 0,
        FOREIGN KEY (parent_id) REFERENCES Documents(id)
      )`);

      // Tabla DocumentImages
      db.run(`CREATE TABLE IF NOT EXISTS DocumentImages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        document_id INTEGER,
        path TEXT,
        caption TEXT,
        order_index INTEGER,
        include_in_compile BOOLEAN DEFAULT 1,
        FOREIGN KEY (document_id) REFERENCES Documents(id)
      )`);

      // Tabla Tags
      db.run(`CREATE TABLE IF NOT EXISTS Tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        color TEXT DEFAULT '#cccccc'
      )`);

      // Tabla DocumentTags
      db.run(`CREATE TABLE IF NOT EXISTS DocumentTags (
        document_id INTEGER,
        tag_id INTEGER,
        PRIMARY KEY (document_id, tag_id),
        FOREIGN KEY (document_id) REFERENCES Documents(id),
        FOREIGN KEY (tag_id) REFERENCES Tags(id)
      )`);

      // Tabla ProjectSettings
      db.run(`CREATE TABLE IF NOT EXISTS ProjectSettings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        value TEXT
      )`);

      // Insertar configuración inicial
      db.run(`INSERT INTO ProjectSettings (name, value) VALUES (?, ?)`, 
        ['project_name', projectName]);
    });

    db.close();

    return { success: true, path: dbPath };
  } catch (error) {
    console.error('Error creating project:', error);
    return { success: false, error: error.message };
  }
});

// Más manejadores de IPC para operaciones de documentos, compilación, etc.