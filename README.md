# <PERSON><PERSON>nberg Writer

Editor de texto avanzado para escritores, novelistas y poetas con estructura jerárquica flexible y compilación personalizable.

## Visión General

<PERSON><PERSON>nberg Writer es una aplicación de escritorio diseñada para escritores que necesitan gestionar proyectos de escritura complejos. Combina las mejores características de aplicaciones como Scrivener, Obsidian, Wavemaker Cards y Ginko en una interfaz moderna y flexible.

## Características Principales

- **Estructura Jerárquica Flexible**: Documentos que pueden contener otros documentos, funcionando como carpetas y contenido simultáneamente.
- **Editor de Texto Rico**: Herramientas completas de formato para crear documentos profesionales.
- **Metadatos Enriquecidos**: Sinopsis, imágenes y etiquetas personalizables para cada documento.
- **Sistema de Etiquetas**: Etiquetas con colores personalizables para organizar y filtrar contenido.
- **Compilación Avanzada**: Selección precisa de qué contenido incluir y en qué orden al exportar.
- **Almacenamiento Local**: Proyectos guardados localmente con posibilidad de respaldo en servicios en la nube.

## Tecnologías

- **Electron**: Para crear una aplicación de escritorio multiplataforma
- **React + TypeScript**: Para la interfaz de usuario
- **Tailwind CSS 4 + shadcn/ui**: Para un diseño moderno y consistente
- **SQLite**: Para almacenamiento local de proyectos y metadatos
- **Vite**: Para desarrollo rápido

## Estructura de la Interfaz

1. **Barra de Menú**: Opciones de archivo, vista, ajustes y herramientas
2. **Barra de Herramientas**: Formato de texto, estilos, listas y otras herramientas de edición
3. **Navegador de Documentos**: Vista jerárquica de documentos y carpetas (izquierda)
4. **Editor Principal**: Área de edición del documento seleccionado
5. **Panel de Metadatos**: Sinopsis, imágenes, etiquetas y estadísticas (derecha)

## Modelo de Datos

- **Documentos**: Elementos que pueden contener texto, metadatos y otros documentos
- **Imágenes**: Asociadas a documentos específicos
- **Etiquetas**: Personalizables con colores para categorizar documentos
- **Perfiles de Compilación**: Configuraciones guardadas para exportar proyectos

## Compilación

El sistema permite exportar documentos seleccionados con control granular sobre:
- Inclusión de títulos, sinopsis y contenido
- Orden de los documentos
- Formato de salida (PDF, Markdown, DOCX, EPUB)
- Estilos y formatos específicos

## Estado del Desarrollo

### ✅ Completado

- **Configuración del proyecto**: Vite + React + TypeScript + Tailwind CSS
- **Componentes UI básicos**: Button, Input, Dialog, DropdownMenu, etc.
- **Estructura de la aplicación**: Layout con 3 paneles redimensionables
- **MenuBar**: Menús de archivo, editar, vista y herramientas
- **Toolbar**: Barra de herramientas de formato de texto
- **DocumentExplorer**: Navegador jerárquico de documentos con datos de ejemplo
- **Editor**: Editor de texto rico con TipTap, soporte para Markdown y HTML
- **MetadataPanel**: Panel de metadatos con sinopsis, etiquetas y configuración
- **ProjectStats**: Componente de estadísticas del proyecto
- **Hooks personalizados**: useProject, useDocuments para manejo de estado
- **Servicio Electron**: Interfaz para comunicación con el proceso principal
- **Tipos TypeScript**: Definiciones completas para el modelo de datos
- **Estilos**: CSS personalizado para el editor y componentes

### 🚧 En Desarrollo

- **Integración con Electron**: Comunicación real con el proceso principal
- **Base de datos SQLite**: Implementación de operaciones CRUD
- **Gestión de archivos**: Guardado y carga de proyectos
- **Sistema de etiquetas**: Funcionalidad completa de etiquetas
- **Auto-guardado**: Guardado automático de cambios

### 📋 Por Hacer

- **Compilación y exportación**: Sistema de exportación a PDF, DOCX, etc.
- **Gestión de imágenes**: Subida y asociación de imágenes
- **Búsqueda y reemplazo**: Funcionalidad de búsqueda global
- **Configuraciones**: Panel de preferencias y configuraciones
- **Atajos de teclado**: Implementación completa de shortcuts
- **Modo enfoque**: Vista de escritura sin distracciones
- **Respaldos automáticos**: Sistema de backup automático
- **Temas**: Soporte para temas claro/oscuro

## Instalación y Desarrollo

```bash
# Instalar dependencias
npm install

# Ejecutar en modo desarrollo (web)
npm run dev

# Ejecutar con Electron (cuando esté configurado)
npm run electron:dev

# Construir para producción
npm run build
```

## Estructura del Proyecto

```
src/
├── components/          # Componentes React
│   ├── ui/             # Componentes UI básicos (shadcn/ui)
│   ├── DocumentExplorer.tsx
│   ├── Editor.tsx
│   ├── MenuBar.tsx
│   ├── MetadataPanel.tsx
│   ├── ProjectStats.tsx
│   └── Toolbar.tsx
├── hooks/              # Hooks personalizados
│   ├── useDocuments.ts
│   └── useProject.ts
├── services/           # Servicios y APIs
│   └── electronService.ts
├── types/              # Definiciones TypeScript
│   └── index.ts
├── App.tsx             # Componente principal
├── main.tsx           # Punto de entrada
└── index.css          # Estilos globales
```

## Desarrollo Futuro

- Sincronización en la nube
- Colaboración en tiempo real
- Aplicación móvil complementaria
- Extensiones y plugins
- Integración con servicios de escritura
- Análisis de texto avanzado
- Generación de contenido con IA