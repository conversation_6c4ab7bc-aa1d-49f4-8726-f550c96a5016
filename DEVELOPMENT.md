# Guía de Desarrollo - <PERSON><PERSON><PERSON> Writer

## Estado Actual del Proyecto

El proyecto G<PERSON>nberg Writer ha sido desarrollado hasta un estado funcional con las siguientes características implementadas:

### ✅ Componentes Completados

1. **Estructura Base**
   - Layout principal con 3 paneles redimensionables
   - Configuración de Vite + React + TypeScript
   - Integración de Tailwind CSS y shadcn/ui

2. **Componentes UI**
   - Button, Input, Textarea, Dialog, DropdownMenu
   - Label, Toast (sistema de notificaciones)
   - Resizable panels para el layout

3. **Componentes Principales**
   - **MenuBar**: Menús completos con opciones de archivo, edición, vista y herramientas
   - **Toolbar**: Barra de herramientas de formato con botones para texto rico
   - **DocumentExplorer**: Navegador jerárquico con datos de ejemplo
   - **Editor**: Editor de texto rico con TipTap, soporte para Markdown/HTML
   - **MetadataPanel**: Panel de metadatos con sinopsis, etiquetas y configuración
   - **ProjectStats**: Estadísticas detalladas del proyecto

4. **Hooks y Servicios**
   - **useProject**: Manejo del estado del proyecto
   - **useDocuments**: Gestión de documentos y navegación
   - **useToast**: Sistema de notificaciones
   - **electronService**: Interfaz para comunicación con Electron (mock)

5. **Tipos y Configuración**
   - Definiciones TypeScript completas
   - Constantes de configuración
   - Estilos CSS personalizados para el editor

## Cómo Probar la Aplicación

### 1. Instalación
```bash
npm install
```

### 2. Ejecutar en Desarrollo
```bash
npm run dev
```

### 3. Abrir en el Navegador
Visita `http://localhost:5173`

### 4. Funcionalidades a Probar

#### Navegación de Documentos
- Expandir/contraer carpetas en el explorador izquierdo
- Seleccionar documentos para editarlos
- Ver la jerarquía de documentos

#### Editor de Texto
- Escribir y formatear texto
- Usar la barra de herramientas para formato
- Probar diferentes estilos (títulos, listas, citas)
- Ver el contador de palabras en tiempo real

#### Panel de Metadatos
- Editar la sinopsis de documentos
- Ver estadísticas del documento
- Explorar las etiquetas disponibles
- Configurar opciones de compilación

#### Menús y Herramientas
- Explorar los menús de la barra superior
- Abrir las estadísticas del proyecto (Herramientas > Estadísticas)
- Probar los diálogos de creación de proyecto

## Datos de Ejemplo

La aplicación incluye datos de ejemplo para demostrar la funcionalidad:

### Documentos
- **Mi Novela** (carpeta)
  - Capítulo 1: El Comienzo
  - Capítulo 2: La Aventura
- **Personajes** (carpeta)
  - Protagonista

### Etiquetas
- Acción (rojo)
- Misterio (púrpura)
- Personaje Principal (azul)
- Desarrollo (verde)

## Próximos Pasos de Desarrollo

### Prioridad Alta
1. **Integración con Electron**
   - Configurar el proceso principal
   - Implementar comunicación IPC
   - Conectar con la base de datos SQLite

2. **Persistencia de Datos**
   - Implementar operaciones CRUD reales
   - Sistema de auto-guardado
   - Gestión de archivos de proyecto

3. **Funcionalidades del Editor**
   - Atajos de teclado
   - Búsqueda y reemplazo
   - Modo enfoque

### Prioridad Media
1. **Sistema de Etiquetas**
   - CRUD completo de etiquetas
   - Filtrado por etiquetas
   - Colores personalizables

2. **Exportación**
   - Compilación a PDF
   - Exportación a DOCX
   - Configuración de formato

3. **Gestión de Imágenes**
   - Subida de imágenes
   - Asociación con documentos
   - Visualización en el editor

### Prioridad Baja
1. **Configuraciones**
   - Panel de preferencias
   - Temas personalizables
   - Configuración de atajos

2. **Funcionalidades Avanzadas**
   - Respaldos automáticos
   - Sincronización en la nube
   - Colaboración

## Estructura de Archivos

```
src/
├── components/
│   ├── ui/                 # Componentes UI básicos
│   ├── DocumentExplorer.tsx
│   ├── Editor.tsx
│   ├── MenuBar.tsx
│   ├── MetadataPanel.tsx
│   ├── ProjectStats.tsx
│   ├── Toolbar.tsx
│   └── ToastContainer.tsx
├── hooks/
│   ├── useDocuments.ts
│   ├── useProject.ts
│   └── useToast.ts
├── services/
│   └── electronService.ts
├── types/
│   └── index.ts
├── config/
│   └── constants.ts
├── lib/
│   └── utils.ts
├── App.tsx
├── main.tsx
└── index.css
```

## Notas Técnicas

### Dependencias Principales
- **React 18**: Framework de UI
- **TypeScript**: Tipado estático
- **Vite**: Build tool y dev server
- **Tailwind CSS**: Framework de CSS
- **TipTap**: Editor de texto rico
- **Radix UI**: Componentes primitivos
- **Lucide React**: Iconos

### Patrones de Desarrollo
- Hooks personalizados para lógica de estado
- Componentes funcionales con TypeScript
- Props interfaces bien definidas
- Separación de responsabilidades
- Datos mock para desarrollo

### Consideraciones de Rendimiento
- Lazy loading preparado para componentes grandes
- Memoización en hooks donde es necesario
- Optimización de re-renders con useCallback
- Estructura preparada para virtualización de listas

## Problemas Conocidos

1. **Datos Mock**: Actualmente usa datos de ejemplo, necesita integración real
2. **Auto-guardado**: Implementado solo en consola, necesita persistencia real
3. **Electron**: Configurado pero no integrado completamente
4. **Atajos de Teclado**: Definidos pero no implementados
5. **Exportación**: UI preparada pero sin funcionalidad backend

## Contribución

Para contribuir al proyecto:

1. Revisar los TODOs en el código
2. Implementar funcionalidades de la lista de prioridades
3. Mantener la consistencia de tipos TypeScript
4. Seguir los patrones de componentes establecidos
5. Probar en navegador antes de integrar con Electron
