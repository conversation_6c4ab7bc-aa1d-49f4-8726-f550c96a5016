// Constantes de configuración para la aplicación

export const APP_CONFIG = {
  name: '<PERSON><PERSON><PERSON> Writer',
  version: '0.1.0',
  description: 'Editor de texto avanzado para escritores',
  author: '<PERSON><PERSON>nberg Writer Team',
};

export const EDITOR_CONFIG = {
  defaultFont: 'Georgia, "Times New Roman", serif',
  defaultFontSize: 16,
  defaultLineHeight: 1.6,
  wordsPerPage: 250,
  wordsPerMinute: 200, // Para estimación de tiempo de lectura
  autoSaveInterval: 30000, // 30 segundos
  maxUndoHistory: 100,
};

export const PROJECT_CONFIG = {
  defaultDailyWordGoal: 1000,
  maxProjectNameLength: 100,
  maxDocumentTitleLength: 200,
  maxSynopsisLength: 1000,
  supportedExportFormats: ['pdf', 'docx', 'markdown', 'epub', 'txt'],
  maxTagsPerDocument: 10,
  maxTagNameLength: 50,
};

export const UI_CONFIG = {
  defaultPanelSizes: {
    explorer: 20,
    editor: 60,
    metadata: 20,
  },
  minPanelSizes: {
    explorer: 15,
    editor: 40,
    metadata: 15,
  },
  maxRecentProjects: 10,
  animationDuration: 200,
};

export const KEYBOARD_SHORTCUTS = {
  // Archivo
  newProject: 'Ctrl+Shift+N',
  openProject: 'Ctrl+O',
  saveProject: 'Ctrl+S',
  exportProject: 'Ctrl+E',
  
  // Edición
  newDocument: 'Ctrl+N',
  newFolder: 'Ctrl+Shift+F',
  deleteDocument: 'Delete',
  renameDocument: 'F2',
  
  // Formato
  bold: 'Ctrl+B',
  italic: 'Ctrl+I',
  underline: 'Ctrl+U',
  strikethrough: 'Ctrl+Shift+X',
  
  // Navegación
  focusExplorer: 'Ctrl+1',
  focusEditor: 'Ctrl+2',
  focusMetadata: 'Ctrl+3',
  
  // Vista
  toggleExplorer: 'Ctrl+Shift+E',
  toggleMetadata: 'Ctrl+Shift+M',
  toggleToolbar: 'Ctrl+Shift+T',
  focusMode: 'F11',
  
  // Herramientas
  showStats: 'Ctrl+Shift+S',
  findReplace: 'Ctrl+H',
  preferences: 'Ctrl+,',
};

export const DEFAULT_TAGS = [
  { name: 'Acción', color: '#ef4444' },
  { name: 'Misterio', color: '#8b5cf6' },
  { name: 'Romance', color: '#ec4899' },
  { name: 'Aventura', color: '#f59e0b' },
  { name: 'Drama', color: '#6b7280' },
  { name: 'Fantasía', color: '#10b981' },
  { name: 'Ciencia Ficción', color: '#06b6d4' },
  { name: 'Terror', color: '#dc2626' },
  { name: 'Comedia', color: '#84cc16' },
  { name: 'Histórico', color: '#92400e' },
];

export const COMPILE_TEMPLATES = {
  novel: {
    name: 'Novela',
    includeTitle: true,
    includeSynopsis: false,
    includeContent: true,
    pageBreakAfterChapter: true,
    numberChapters: true,
    fontSize: 12,
    fontFamily: 'Times New Roman',
    lineSpacing: 2,
    margins: { top: 1, bottom: 1, left: 1, right: 1 },
  },
  screenplay: {
    name: 'Guión',
    includeTitle: true,
    includeSynopsis: true,
    includeContent: true,
    pageBreakAfterChapter: false,
    numberChapters: false,
    fontSize: 12,
    fontFamily: 'Courier New',
    lineSpacing: 1,
    margins: { top: 1, bottom: 1, left: 1.5, right: 1 },
  },
  manuscript: {
    name: 'Manuscrito',
    includeTitle: true,
    includeSynopsis: false,
    includeContent: true,
    pageBreakAfterChapter: true,
    numberChapters: true,
    fontSize: 12,
    fontFamily: 'Times New Roman',
    lineSpacing: 2,
    margins: { top: 1, bottom: 1, left: 1, right: 1 },
  },
};

export const FILE_EXTENSIONS = {
  project: '.gwp', // Gutenberg Writer Project
  backup: '.gwb',  // Gutenberg Writer Backup
  template: '.gwt', // Gutenberg Writer Template
  export: {
    pdf: '.pdf',
    docx: '.docx',
    markdown: '.md',
    epub: '.epub',
    txt: '.txt',
  },
};

export const ERROR_MESSAGES = {
  projectNotFound: 'No se pudo encontrar el proyecto especificado',
  projectCorrupted: 'El archivo del proyecto está corrupto',
  saveError: 'Error al guardar el documento',
  loadError: 'Error al cargar el documento',
  exportError: 'Error al exportar el proyecto',
  networkError: 'Error de conexión',
  permissionError: 'No tienes permisos para realizar esta acción',
  diskSpaceError: 'No hay suficiente espacio en disco',
  invalidFormat: 'Formato de archivo no válido',
  documentNotFound: 'No se pudo encontrar el documento',
};

export const SUCCESS_MESSAGES = {
  projectCreated: 'Proyecto creado exitosamente',
  projectSaved: 'Proyecto guardado exitosamente',
  documentCreated: 'Documento creado exitosamente',
  documentSaved: 'Documento guardado exitosamente',
  documentDeleted: 'Documento eliminado exitosamente',
  exported: 'Proyecto exportado exitosamente',
  tagCreated: 'Etiqueta creada exitosamente',
  settingsSaved: 'Configuraciones guardadas exitosamente',
};
