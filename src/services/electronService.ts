// Servicio para comunicación con Electron
// Este archivo maneja toda la comunicación entre el renderer y el main process

interface ElectronAPI {
  createProject: (projectName: string, projectPath: string) => Promise<{ success: boolean; path?: string; error?: string }>;
  openProject: (projectPath: string) => Promise<{ success: boolean; data?: any; error?: string }>;
  saveDocument: (documentId: number, content: string) => Promise<{ success: boolean; error?: string }>;
  loadDocuments: () => Promise<{ success: boolean; documents?: any[]; error?: string }>;
  createDocument: (document: any) => Promise<{ success: boolean; id?: number; error?: string }>;
  updateDocument: (documentId: number, updates: any) => Promise<{ success: boolean; error?: string }>;
  deleteDocument: (documentId: number) => Promise<{ success: boolean; error?: string }>;
  loadTags: () => Promise<{ success: boolean; tags?: any[]; error?: string }>;
  createTag: (tag: any) => Promise<{ success: boolean; id?: number; error?: string }>;
  updateTag: (tagId: number, updates: any) => Promise<{ success: boolean; error?: string }>;
  deleteTag: (tagId: number) => Promise<{ success: boolean; error?: string }>;
  exportProject: (config: any) => Promise<{ success: boolean; path?: string; error?: string }>;
}

// Verificar si estamos en el contexto de Electron
const isElectron = () => {
  return typeof window !== 'undefined' && window.process && window.process.type;
};

// Mock API para desarrollo en navegador
const mockElectronAPI: ElectronAPI = {
  createProject: async (projectName: string, projectPath: string) => {
    console.log('Mock: Creating project', projectName, projectPath);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simular delay
    return { success: true, path: projectPath };
  },

  openProject: async (projectPath: string) => {
    console.log('Mock: Opening project', projectPath);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { 
      success: true, 
      data: {
        name: 'Mi Novela',
        path: projectPath,
        documents: [],
        tags: [],
        settings: []
      }
    };
  },

  saveDocument: async (documentId: number, content: string) => {
    console.log('Mock: Saving document', documentId, content.length, 'characters');
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },

  loadDocuments: async () => {
    console.log('Mock: Loading documents');
    await new Promise(resolve => setTimeout(resolve, 500));
    return { 
      success: true, 
      documents: [
        {
          id: 1,
          title: 'Mi Novela',
          content: '',
          synopsis: 'Una historia épica sobre...',
          order_index: 0,
          is_folder: true,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          include_in_compile: true,
          compile_content: true,
          compile_title: true,
          compile_synopsis: false,
        }
      ]
    };
  },

  createDocument: async (document: any) => {
    console.log('Mock: Creating document', document);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, id: Date.now() };
  },

  updateDocument: async (documentId: number, updates: any) => {
    console.log('Mock: Updating document', documentId, updates);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true };
  },

  deleteDocument: async (documentId: number) => {
    console.log('Mock: Deleting document', documentId);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },

  loadTags: async () => {
    console.log('Mock: Loading tags');
    await new Promise(resolve => setTimeout(resolve, 300));
    return { 
      success: true, 
      tags: [
        { id: 1, name: 'Acción', color: '#ef4444' },
        { id: 2, name: 'Misterio', color: '#8b5cf6' },
        { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
        { id: 4, name: 'Desarrollo', color: '#10b981' },
      ]
    };
  },

  createTag: async (tag: any) => {
    console.log('Mock: Creating tag', tag);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true, id: Date.now() };
  },

  updateTag: async (tagId: number, updates: any) => {
    console.log('Mock: Updating tag', tagId, updates);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true };
  },

  deleteTag: async (tagId: number) => {
    console.log('Mock: Deleting tag', tagId);
    await new Promise(resolve => setTimeout(resolve, 300));
    return { success: true };
  },

  exportProject: async (config: any) => {
    console.log('Mock: Exporting project', config);
    await new Promise(resolve => setTimeout(resolve, 2000));
    return { success: true, path: '/path/to/exported/file.pdf' };
  },
};

// API real de Electron (se implementará cuando esté disponible)
const getElectronAPI = (): ElectronAPI => {
  if (isElectron() && (window as any).electronAPI) {
    return (window as any).electronAPI;
  }
  
  // Fallback al mock para desarrollo
  return mockElectronAPI;
};

// Exportar el servicio
export const electronService = getElectronAPI();

// Funciones de utilidad
export const ElectronService = {
  // Proyectos
  createProject: (name: string, path: string) => electronService.createProject(name, path),
  openProject: (path: string) => electronService.openProject(path),
  
  // Documentos
  saveDocument: (id: number, content: string) => electronService.saveDocument(id, content),
  loadDocuments: () => electronService.loadDocuments(),
  createDocument: (document: any) => electronService.createDocument(document),
  updateDocument: (id: number, updates: any) => electronService.updateDocument(id, updates),
  deleteDocument: (id: number) => electronService.deleteDocument(id),
  
  // Etiquetas
  loadTags: () => electronService.loadTags(),
  createTag: (tag: any) => electronService.createTag(tag),
  updateTag: (id: number, updates: any) => electronService.updateTag(id, updates),
  deleteTag: (id: number) => electronService.deleteTag(id),
  
  // Exportación
  exportProject: (config: any) => electronService.exportProject(config),
  
  // Utilidades
  isElectron: isElectron(),
};
