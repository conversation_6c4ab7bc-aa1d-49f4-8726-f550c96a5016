// Servicio para manejar archivos del sistema
// Por ahora usaremos APIs web, luego se integrará con Electron

export interface FileSystemService {
  selectFolder(): Promise<string | null>;
  selectFile(): Promise<string | null>;
  createProject(name: string, path: string): Promise<boolean>;
  openProject(path: string): Promise<any>;
  saveProject(projectData: any, path: string): Promise<boolean>;
}

class WebFileSystemService implements FileSystemService {
  
  async selectFolder(): Promise<string | null> {
    // En el navegador, simularemos la selección de carpeta
    if ('showDirectoryPicker' in window) {
      try {
        // @ts-ignore - API experimental
        const dirHandle = await window.showDirectoryPicker();
        return dirHandle.name;
      } catch (err) {
        console.log('Usuario canceló la selección de carpeta');
        return null;
      }
    } else {
      // Fallback para navegadores que no soportan File System Access API
      const folderPath = prompt(
        'Ingresa la ruta donde quieres crear el proyecto:\n' +
        '(En la versión final de Electron se abrirá un selector de carpetas)'
      );
      return folderPath;
    }
  }

  async selectFile(): Promise<string | null> {
    // En el navegador, simularemos la selección de archivo
    if ('showOpenFilePicker' in window) {
      try {
        // @ts-ignore - API experimental
        const [fileHandle] = await window.showOpenFilePicker({
          types: [{
            description: 'Proyectos de Gutenberg Writer',
            accept: {
              'application/json': ['.gwp', '.json']
            }
          }]
        });
        return fileHandle.name;
      } catch (err) {
        console.log('Usuario canceló la selección de archivo');
        return null;
      }
    } else {
      // Fallback para navegadores que no soportan File System Access API
      const filePath = prompt(
        'Ingresa la ruta del archivo de proyecto a abrir:\n' +
        '(En la versión final de Electron se abrirá un selector de archivos)'
      );
      return filePath;
    }
  }

  async createProject(name: string, path: string): Promise<boolean> {
    try {
      // Simular creación de proyecto
      console.log(`Creando proyecto "${name}" en: ${path}`);
      
      // En la versión real con Electron, aquí se crearían los archivos
      const projectData = {
        name,
        path,
        createdAt: new Date().toISOString(),
        version: '1.0.0',
        documents: [],
        tags: [
          { id: 1, name: 'Acción', color: '#ef4444' },
          { id: 2, name: 'Misterio', color: '#8b5cf6' },
          { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
          { id: 4, name: 'Desarrollo', color: '#10b981' },
        ],
        settings: [
          { id: 1, name: 'project_name', value: name },
        ],
      };

      // Guardar en localStorage por ahora
      localStorage.setItem(`project_${name}`, JSON.stringify(projectData));
      localStorage.setItem('lastProjectPath', path);
      
      return true;
    } catch (error) {
      console.error('Error creando proyecto:', error);
      return false;
    }
  }

  async openProject(path: string): Promise<any> {
    try {
      console.log(`Abriendo proyecto desde: ${path}`);
      
      // En la versión real con Electron, aquí se leería el archivo
      // Por ahora, buscaremos en localStorage
      const projectName = path.split('/').pop() || path;
      const projectData = localStorage.getItem(`project_${projectName}`);
      
      if (projectData) {
        return JSON.parse(projectData);
      } else {
        // Crear proyecto de ejemplo si no existe
        const exampleProject = {
          name: projectName,
          path,
          createdAt: new Date().toISOString(),
          version: '1.0.0',
          documents: [],
          tags: [
            { id: 1, name: 'Acción', color: '#ef4444' },
            { id: 2, name: 'Misterio', color: '#8b5cf6' },
            { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
            { id: 4, name: 'Desarrollo', color: '#10b981' },
          ],
          settings: [
            { id: 1, name: 'project_name', value: projectName },
          ],
        };
        
        localStorage.setItem(`project_${projectName}`, JSON.stringify(exampleProject));
        return exampleProject;
      }
    } catch (error) {
      console.error('Error abriendo proyecto:', error);
      throw new Error(`No se pudo abrir el proyecto: ${error}`);
    }
  }

  async saveProject(projectData: any, path: string): Promise<boolean> {
    try {
      console.log(`Guardando proyecto en: ${path}`);
      
      // En la versión real con Electron, aquí se escribiría el archivo
      localStorage.setItem(`project_${projectData.name}`, JSON.stringify(projectData));
      localStorage.setItem('lastProjectPath', path);
      
      return true;
    } catch (error) {
      console.error('Error guardando proyecto:', error);
      return false;
    }
  }
}

// Servicio singleton
export const fileService: FileSystemService = new WebFileSystemService();

// Función para detectar si estamos en Electron
export function isElectron(): boolean {
  return typeof window !== 'undefined' && window.process && window.process.type;
}

// En el futuro, cuando integremos con Electron:
// if (isElectron()) {
//   fileService = new ElectronFileSystemService();
// } else {
//   fileService = new WebFileSystemService();
// }
