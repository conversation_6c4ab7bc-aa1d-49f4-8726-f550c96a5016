import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from './components/ui/resizable'
import { DocumentExplorer } from './components/DocumentExplorer'
import { Editor } from './components/Editor'
import { MetadataPanel } from './components/MetadataPanel'
import { Toolbar } from './components/Toolbar'
import { MenuBar } from './components/MenuBar'
import { useDocuments } from './hooks/useDocuments'
import { useProject } from './hooks/useProject'

function App() {
  const {
    selectedDocumentId,
    setSelectedDocumentId,
    documents,
    isLoading: documentsLoading,
    error: documentsError
  } = useDocuments();

  const {
    currentProject,
    isLoading: projectLoading,
    error: projectError
  } = useProject();

  return (
    <div className="flex flex-col h-screen">
      <MenuBar documents={documents} />
      <Toolbar />

      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Panel izquierdo: Explorador de documentos */}
          <ResizablePanel defaultSize={20} minSize={15}>
            <DocumentExplorer
              onSelectDocument={setSelectedDocumentId}
              selectedDocumentId={selectedDocumentId}
            />
          </ResizablePanel>

          <ResizableHandle />

          {/* Panel central: Editor */}
          <ResizablePanel defaultSize={60}>
            <Editor documentId={selectedDocumentId} />
          </ResizablePanel>

          <ResizableHandle />

          {/* Panel derecho: Metadatos */}
          <ResizablePanel defaultSize={20} minSize={15}>
            <MetadataPanel documentId={selectedDocumentId} />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      <div className="p-2 border-t text-sm text-gray-500 flex items-center justify-between">
        <span>Gutenberg Writer v0.1.0</span>
        {(documentsLoading || projectLoading) && (
          <span className="text-xs">Cargando...</span>
        )}
        {(documentsError || projectError) && (
          <span className="text-xs text-red-500">Error: {documentsError || projectError}</span>
        )}
      </div>
    </div>
  )
}

export default App