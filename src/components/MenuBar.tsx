import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { Button } from './ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { ProjectStats } from './ProjectStats';
import {
  FileText,
  FolderOpen,
  Save,
  Settings,
  Eye,
  Download,
  Plus,
  Folder,
  BarChart3
} from 'lucide-react';

interface MenuBarProps {
  documents?: any[];
}

export function MenuBar({ documents = [] }: MenuBarProps) {
  const [isNewProjectOpen, setIsNewProjectOpen] = useState(false);
  const [isStatsOpen, setIsStatsOpen] = useState(false);
  const [projectName, setProjectName] = useState('');

  const handleNewProject = () => {
    if (projectName.trim()) {
      // TODO: Implementar creación de proyecto
      console.log('Crear proyecto:', projectName);
      setProjectName('');
      setIsNewProjectOpen(false);
    }
  };

  const handleOpenProject = () => {
    // TODO: Implementar apertura de proyecto
    console.log('Abrir proyecto');
  };

  const handleSaveProject = () => {
    // TODO: Implementar guardado de proyecto
    console.log('Guardar proyecto');
  };

  const handleExport = () => {
    // TODO: Implementar exportación
    console.log('Exportar proyecto');
  };

  return (
    <div className="flex items-center h-10 px-2 border-b bg-background">
      {/* Menú Archivo */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8">
            Archivo
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuLabel>Proyecto</DropdownMenuLabel>
          <Dialog open={isNewProjectOpen} onOpenChange={setIsNewProjectOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <Plus className="mr-2 h-4 w-4" />
                Nuevo Proyecto
              </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Crear Nuevo Proyecto</DialogTitle>
                <DialogDescription>
                  Ingresa el nombre para tu nuevo proyecto de escritura.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Nombre
                  </Label>
                  <Input
                    id="name"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    className="col-span-3"
                    placeholder="Mi Novela"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button 
                  type="submit" 
                  onClick={handleNewProject}
                  disabled={!projectName.trim()}
                >
                  Crear Proyecto
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <DropdownMenuItem onClick={handleOpenProject}>
            <FolderOpen className="mr-2 h-4 w-4" />
            Abrir Proyecto
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={handleSaveProject}>
            <Save className="mr-2 h-4 w-4" />
            Guardar
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Exportar...
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Menú Editar */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8">
            Editar
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem>
            <FileText className="mr-2 h-4 w-4" />
            Nuevo Documento
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Folder className="mr-2 h-4 w-4" />
            Nueva Carpeta
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Deshacer</DropdownMenuItem>
          <DropdownMenuItem>Rehacer</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Cortar</DropdownMenuItem>
          <DropdownMenuItem>Copiar</DropdownMenuItem>
          <DropdownMenuItem>Pegar</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Menú Vista */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8">
            Vista
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem>
            <Eye className="mr-2 h-4 w-4" />
            Modo Enfoque
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>Mostrar Explorador</DropdownMenuItem>
          <DropdownMenuItem>Mostrar Metadatos</DropdownMenuItem>
          <DropdownMenuItem>Mostrar Barra de Herramientas</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Menú Herramientas */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8">
            Herramientas
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem onClick={() => setIsStatsOpen(true)}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Estadísticas del Proyecto
          </DropdownMenuItem>
          <DropdownMenuItem>Buscar y Reemplazar</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            Preferencias
          </DropdownMenuItem>
          <DropdownMenuItem>Buscar y Reemplazar</DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <Settings className="mr-2 h-4 w-4" />
            Preferencias
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="flex-1" />

      {/* Información del proyecto */}
      <div className="text-sm text-muted-foreground">
        Sin proyecto abierto
      </div>

      {/* Componente de estadísticas */}
      <ProjectStats
        documents={documents}
        isOpen={isStatsOpen}
        onOpenChange={setIsStatsOpen}
      />
    </div>
  );
}
