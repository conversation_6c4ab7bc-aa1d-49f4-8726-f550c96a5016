import { Button } from '../../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../ui/dropdown-menu';
import {
  Plus,
  Palette,
  Copy,
  Trash2,
  AlignLeft,
  AlignCenter,
  Grid3X3,
  Download,
  Upload,
  Users,
  MapPin,
  Lightbulb,
  StickyNote,
} from 'lucide-react';

interface CorkboardToolbarProps {
  selectedCardsCount: number;
  onAddCard: () => void;
  onCreateFromTemplate: (template: 'character' | 'location' | 'idea' | 'note') => void;
  onDuplicateSelected: () => void;
  onDeleteSelected: () => void;
  onChangeSelectedColor: (color: string) => void;
  onAlignCards: (alignment: 'left' | 'center' | 'grid') => void;
  onExportCorkboard: () => void;
  onImportCards: () => void;
}

// Colores predefinidos para las tarjetas
const cardColors = [
  { name: 'Amarillo', value: '#fbbf24', bg: 'bg-yellow-400' },
  { name: '<PERSON><PERSON><PERSON>', value: '#f97316', bg: 'bg-orange-500' },
  { name: 'Rojo', value: '#ef4444', bg: 'bg-red-500' },
  { name: 'Rosa', value: '#ec4899', bg: 'bg-pink-500' },
  { name: 'Púrpura', value: '#8b5cf6', bg: 'bg-purple-500' },
  { name: 'Azul', value: '#3b82f6', bg: 'bg-blue-500' },
  { name: 'Cian', value: '#06b6d4', bg: 'bg-cyan-500' },
  { name: 'Verde', value: '#10b981', bg: 'bg-emerald-500' },
  { name: 'Lima', value: '#84cc16', bg: 'bg-lime-500' },
  { name: 'Gris', value: '#6b7280', bg: 'bg-gray-500' },
];

export function CorkboardToolbar({
  selectedCardsCount,
  onAddCard,
  onCreateFromTemplate,
  onDuplicateSelected,
  onDeleteSelected,
  onChangeSelectedColor,
  onAlignCards,
  onExportCorkboard,
  onImportCards,
}: CorkboardToolbarProps) {
  
  return (
    <div className="flex items-center gap-2 p-2 border-b bg-white">
      {/* Agregar tarjetas */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="default" size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Agregar
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          <DropdownMenuItem onClick={onAddCard}>
            <StickyNote className="mr-2 h-4 w-4" />
            Tarjeta en Blanco
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onCreateFromTemplate('character')}>
            <Users className="mr-2 h-4 w-4" />
            Personaje
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onCreateFromTemplate('location')}>
            <MapPin className="mr-2 h-4 w-4" />
            Ubicación
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onCreateFromTemplate('idea')}>
            <Lightbulb className="mr-2 h-4 w-4" />
            Idea
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onCreateFromTemplate('note')}>
            <StickyNote className="mr-2 h-4 w-4" />
            Nota
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <div className="h-6 w-px bg-border" />

      {/* Acciones para tarjetas seleccionadas */}
      {selectedCardsCount > 0 && (
        <>
          <span className="text-sm text-muted-foreground">
            {selectedCardsCount} seleccionada{selectedCardsCount !== 1 ? 's' : ''}
          </span>

          {/* Cambiar color */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Palette className="h-4 w-4 mr-1" />
                Color
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <div className="grid grid-cols-5 gap-1 p-2">
                {cardColors.map((color) => (
                  <button
                    key={color.value}
                    className={`w-6 h-6 rounded ${color.bg} border-2 border-white hover:border-gray-300`}
                    onClick={() => onChangeSelectedColor(color.value)}
                    title={color.name}
                  />
                ))}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Duplicar */}
          <Button
            variant="outline"
            size="sm"
            onClick={onDuplicateSelected}
          >
            <Copy className="h-4 w-4 mr-1" />
            Duplicar
          </Button>

          {/* Alinear */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <AlignLeft className="h-4 w-4 mr-1" />
                Alinear
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              <DropdownMenuItem onClick={() => onAlignCards('left')}>
                <AlignLeft className="mr-2 h-4 w-4" />
                Alinear a la Izquierda
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAlignCards('center')}>
                <AlignCenter className="mr-2 h-4 w-4" />
                Centrar
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAlignCards('grid')}>
                <Grid3X3 className="mr-2 h-4 w-4" />
                Organizar en Cuadrícula
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Eliminar */}
          <Button
            variant="destructive"
            size="sm"
            onClick={onDeleteSelected}
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Eliminar
          </Button>

          <div className="h-6 w-px bg-border" />
        </>
      )}

      {/* Spacer */}
      <div className="flex-1" />

      {/* Acciones del corkboard */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            Exportar
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={onExportCorkboard}>
            <Download className="mr-2 h-4 w-4" />
            Exportar como Imagen
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onImportCards}>
            <Upload className="mr-2 h-4 w-4" />
            Importar Tarjetas
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
