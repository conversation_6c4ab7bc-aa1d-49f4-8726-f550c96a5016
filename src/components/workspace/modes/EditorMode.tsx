import { useEffect, useState, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import { Button } from '../../ui/button';
import { Toolbar } from '../../Toolbar';
import { FileText, Folder } from 'lucide-react';
import { Document, WorkspaceInstance } from '../../../types';

interface EditorModeProps {
  instance: WorkspaceInstance;
  onUpdateData: (data: any) => void;
}

// Datos de ejemplo para desarrollo (mismo que el Editor original)
const mockDocuments: Record<number, Document> = {
  2: {
    id: 2,
    title: 'Capítulo 1: El Comienzo',
    content: '<p>Era una noche oscura y tormentosa cuando todo comenzó...</p><p>El protagonista se encontraba en su habitación, leyendo un viejo libro que había encontrado en el ático de su abuela. Las páginas amarillentas crujían bajo sus dedos mientras descubría secretos que cambiarían su vida para siempre.</p><h2>El Descubrimiento</h2><p>Entre las páginas del libro, encontró una carta que parecía estar dirigida a él, aunque había sido escrita décadas antes de su nacimiento. La carta hablaba de un poder ancestral que corría por sus venas, un poder que pronto tendría que aprender a controlar.</p>',
    synopsis: 'El protagonista descubre su destino',
    order_index: 0,
    parent_id: 1,
    is_folder: false,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    include_in_compile: true,
    compile_content: true,
    compile_title: true,
    compile_synopsis: false,
  },
  3: {
    id: 3,
    title: 'Capítulo 2: La Aventura',
    content: '<p>El viaje comenzó al amanecer del día siguiente...</p><p>Con la carta en su bolsillo y una mochila llena de provisiones, el protagonista se dirigió hacia el bosque que se extendía más allá de los límites de su pueblo natal.</p><blockquote><p>"El camino hacia el poder verdadero nunca es fácil, pero siempre vale la pena recorrerlo."</p></blockquote><p>Estas palabras de su abuela resonaban en su mente mientras se adentraba en lo desconocido.</p>',
    synopsis: 'Primeros desafíos del héroe',
    order_index: 1,
    parent_id: 1,
    is_folder: false,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    include_in_compile: true,
    compile_content: true,
    compile_title: true,
    compile_synopsis: false,
  },
  5: {
    id: 5,
    title: 'Protagonista',
    content: '<h1>Perfil del Protagonista</h1><h2>Información Básica</h2><ul><li><strong>Nombre:</strong> Alex Meridian</li><li><strong>Edad:</strong> 17 años</li><li><strong>Origen:</strong> Pueblo de Eldermere</li></ul><h2>Características</h2><p>Alex es un joven introvertido con una curiosidad insaciable por los misterios del mundo. Desde pequeño ha sentido que no encaja completamente en su entorno, como si estuviera destinado a algo más grande.</p><h2>Habilidades</h2><ul><li>Intuición desarrollada</li><li>Capacidad de aprendizaje rápido</li><li>Conexión especial con la naturaleza</li></ul>',
    synopsis: 'Héroe de la historia',
    order_index: 0,
    parent_id: 4,
    is_folder: false,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    include_in_compile: false,
    compile_content: false,
    compile_title: false,
    compile_synopsis: false,
  },
};

export function EditorMode({ instance, onUpdateData }: EditorModeProps) {
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [isModified, setIsModified] = useState(false);

  // Función para manejar formato de texto
  const handleFormatText = useCallback((format: string) => {
    if (!editor) return;

    switch (format) {
      case 'bold':
        editor.chain().focus().toggleBold().run();
        break;
      case 'italic':
        editor.chain().focus().toggleItalic().run();
        break;
      case 'underline':
        editor.chain().focus().toggleUnderline().run();
        break;
      case 'strikethrough':
        editor.chain().focus().toggleStrike().run();
        break;
      case 'alignLeft':
        editor.chain().focus().setTextAlign('left').run();
        break;
      case 'alignCenter':
        editor.chain().focus().setTextAlign('center').run();
        break;
      case 'alignRight':
        editor.chain().focus().setTextAlign('right').run();
        break;
      case 'alignJustify':
        editor.chain().focus().setTextAlign('justify').run();
        break;
      case 'bulletList':
        editor.chain().focus().toggleBulletList().run();
        break;
      case 'orderedList':
        editor.chain().focus().toggleOrderedList().run();
        break;
      case 'blockquote':
        editor.chain().focus().toggleBlockquote().run();
        break;
      case 'code':
        editor.chain().focus().toggleCode().run();
        break;
      case 'heading1':
        editor.chain().focus().toggleHeading({ level: 1 }).run();
        break;
      case 'heading2':
        editor.chain().focus().toggleHeading({ level: 2 }).run();
        break;
      case 'heading3':
        editor.chain().focus().toggleHeading({ level: 3 }).run();
        break;
      case 'paragraph':
        editor.chain().focus().setParagraph().run();
        break;
      case 'undo':
        editor.chain().focus().undo().run();
        break;
      case 'redo':
        editor.chain().focus().redo().run();
        break;
      default:
        console.log('Formato no reconocido:', format);
    }
  }, [editor]);

  // Función para insertar elementos
  const handleInsertElement = useCallback((element: string) => {
    if (!editor) return;

    switch (element) {
      case 'link':
        const url = window.prompt('URL del enlace:');
        if (url) {
          editor.chain().focus().setLink({ href: url }).run();
        }
        break;
      case 'image':
        const imageUrl = window.prompt('URL de la imagen:');
        if (imageUrl) {
          // TODO: Implementar inserción de imágenes
          console.log('Insertar imagen:', imageUrl);
        }
        break;
      default:
        console.log('Elemento no reconocido:', element);
    }
  }, [editor]);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Placeholder.configure({
        placeholder: 'Comienza a escribir tu historia...',
      }),
      CharacterCount,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      Link.configure({
        openOnClick: false,
      }),
    ],
    content: '',
    onUpdate: ({ editor }) => {
      setIsModified(true);
      // Guardar contenido en los datos de la instancia
      const content = editor.getHTML();
      onUpdateData({ 
        content,
        lastModified: new Date().toISOString(),
        wordCount: editor.storage.characterCount.words(),
        characterCount: editor.storage.characterCount.characters(),
      });
      console.log('Contenido actualizado en EditorMode:', content);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-full p-8',
      },
    },
  });

  // Cargar documento cuando cambia la instancia
  useEffect(() => {
    const documentId = instance.documentId;
    if (documentId && mockDocuments[documentId]) {
      const document = mockDocuments[documentId];
      setCurrentDocument(document);
      
      // Cargar contenido desde los datos de la instancia o desde el documento
      const savedContent = instance.data?.content || document.content || '';
      editor?.commands.setContent(savedContent);
      setIsModified(false);
    } else {
      setCurrentDocument(null);
      editor?.commands.setContent('');
      setIsModified(false);
    }
  }, [instance, editor]);

  // Función para guardar el documento
  const saveDocument = () => {
    if (currentDocument && editor) {
      const content = editor.getHTML();
      console.log('Guardando documento:', currentDocument.id, content);
      // TODO: Implementar guardado real
      setIsModified(false);
      
      // Actualizar datos de la instancia
      onUpdateData({
        content,
        lastSaved: new Date().toISOString(),
        wordCount: editor.storage.characterCount.words(),
        characterCount: editor.storage.characterCount.characters(),
      });
    }
  };

  // Atajos de teclado
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        saveDocument();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [currentDocument, editor]);

  // Obtener estadísticas del editor
  const wordCount = editor?.storage.characterCount.words() || 0;
  const characterCount = editor?.storage.characterCount.characters() || 0;

  if (!instance.documentId || !currentDocument) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Selecciona un documento para comenzar a escribir</p>
          <p className="text-sm">Elige un documento del explorador de la izquierda</p>
        </div>
      </div>
    );
  }

  if (currentDocument.is_folder) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <Folder className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Esta es una carpeta</p>
          <p className="text-sm">Selecciona un documento para editarlo</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar del editor */}
      <Toolbar
        onFormatText={handleFormatText}
        onInsertElement={handleInsertElement}
        canUndo={editor?.can().undo() || false}
        canRedo={editor?.can().redo() || false}
        wordCount={wordCount}
        characterCount={characterCount}
      />

      {/* Encabezado del documento */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-muted-foreground" />
          <h1 className="text-lg font-semibold">{currentDocument.title}</h1>
          {isModified && (
            <span className="text-xs text-muted-foreground">• Sin guardar</span>
          )}
          <span className="text-xs text-muted-foreground bg-blue-50 px-2 py-1 rounded">
            {instance.name}
          </span>
        </div>
        
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{wordCount} palabras</span>
          <span>{characterCount} caracteres</span>
          {isModified && (
            <Button
              variant="outline"
              size="sm"
              onClick={saveDocument}
              className="h-7"
            >
              Guardar
            </Button>
          )}
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-auto bg-white">
        <EditorContent 
          editor={editor} 
          className="h-full"
        />
      </div>
    </div>
  );
}
