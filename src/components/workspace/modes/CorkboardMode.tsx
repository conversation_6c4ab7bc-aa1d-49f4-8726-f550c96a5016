import { useState, useRef, useCallback } from 'react';
import { useCorkboard } from '../../../hooks/useCorkboard';
import { CorkboardCard } from './corkboard/CorkboardCard';
import { CorkboardToolbar } from '../toolbars/CorkboardToolbar';
import { WorkspaceInstance } from '../../../types';
import { Plus, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { Button } from '../../ui/button';

interface CorkboardModeProps {
  instance: WorkspaceInstance;
  onUpdateData: (data: any) => void;
}

export function CorkboardMode({ instance, onUpdateData }: CorkboardModeProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const {
    cards,
    selectedCards,
    addCard,
    updateCard,
    deleteCard,
    selectCard,
    clearSelection,
    moveCard,
    createCardFromTemplate,
    duplicateSelectedCards,
    deleteSelectedCards,
    changeSelectedCardsColor,
    alignSelectedCards,
    getStats,
  } = useCorkboard(instance, onUpdateData);

  // Manejar zoom
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev * 1.2, 3));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev / 1.2, 0.3));
  }, []);

  const handleResetView = useCallback(() => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  }, []);

  // Manejar pan (arrastrar el tablero)
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === containerRef.current || (e.target as HTMLElement).classList.contains('corkboard-background')) {
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
      clearSelection();
    }
  }, [pan, clearSelection]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Agregar nueva tarjeta
  const handleAddCard = useCallback(() => {
    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    // Calcular posición en el centro visible del tablero
    const centerX = (rect.width / 2 - pan.x) / zoom;
    const centerY = (rect.height / 2 - pan.y) / zoom;

    addCard({
      x: centerX - 100, // Centrar la tarjeta (ancho 200px / 2)
      y: centerY - 75,  // Centrar la tarjeta (alto 150px / 2)
      width: 200,
      height: 150,
      color: '#fbbf24', // Amarillo por defecto
      content: 'Nueva tarjeta',
    });
  }, [addCard, pan, zoom]);

  // Manejar doble clic para agregar tarjeta
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    if (e.target === containerRef.current || (e.target as HTMLElement).classList.contains('corkboard-background')) {
      const rect = containerRef.current?.getBoundingClientRect();
      if (!rect) return;

      const x = (e.clientX - rect.left - pan.x) / zoom;
      const y = (e.clientY - rect.top - pan.y) / zoom;

      addCard({
        x: x - 100,
        y: y - 75,
        width: 200,
        height: 150,
        color: '#fbbf24',
        content: 'Nueva tarjeta',
      });
    }
  }, [addCard, pan, zoom]);

  // Funciones para la toolbar
  const handleExportCorkboard = useCallback(() => {
    console.log('Exportar corkboard - TODO: implementar');
  }, []);

  const handleImportCards = useCallback(() => {
    console.log('Importar tarjetas - TODO: implementar');
  }, []);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Toolbar del Corkboard */}
      <CorkboardToolbar
        selectedCardsCount={selectedCards.length}
        onAddCard={handleAddCard}
        onCreateFromTemplate={createCardFromTemplate}
        onDuplicateSelected={duplicateSelectedCards}
        onDeleteSelected={deleteSelectedCards}
        onChangeSelectedColor={changeSelectedCardsColor}
        onAlignCards={alignSelectedCards}
        onExportCorkboard={handleExportCorkboard}
        onImportCards={handleImportCards}
      />

      {/* Controles de vista */}
      <div className="flex items-center justify-between p-2 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-muted-foreground">
            {instance.name}
          </span>
          <span className="text-xs text-muted-foreground">
            {cards.length} tarjeta{cards.length !== 1 ? 's' : ''}
          </span>
          {selectedCards.length > 0 && (
            <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
              {selectedCards.length} seleccionada{selectedCards.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Controles de zoom */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomOut}
            className="h-7 w-7 p-0"
          >
            <ZoomOut className="h-3 w-3" />
          </Button>

          <span className="text-xs text-muted-foreground min-w-12 text-center">
            {Math.round(zoom * 100)}%
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={handleZoomIn}
            className="h-7 w-7 p-0"
          >
            <ZoomIn className="h-3 w-3" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleResetView}
            className="h-7 w-7 p-0"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Área del tablero */}
      <div
        ref={containerRef}
        className="flex-1 overflow-hidden cursor-grab active:cursor-grabbing corkboard-background"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onDoubleClick={handleDoubleClick}
        style={{
          backgroundImage: `
            radial-gradient(circle, #d1d5db 1px, transparent 1px)
          `,
          backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
          backgroundPosition: `${pan.x}px ${pan.y}px`,
        }}
      >
        {/* Contenedor de tarjetas con transformación */}
        <div
          className="relative w-full h-full"
          style={{
            transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
            transformOrigin: '0 0',
          }}
        >
          {/* Renderizar tarjetas */}
          {cards.map((card) => (
            <CorkboardCard
              key={card.id}
              card={card}
              isSelected={selectedCards.includes(card.id)}
              onUpdate={(updates) => updateCard(card.id, updates)}
              onDelete={() => deleteCard(card.id)}
              onSelect={() => selectCard(card.id)}
              onMove={(deltaX, deltaY) => moveCard(card.id, deltaX, deltaY)}
              zoom={zoom}
            />
          ))}

          {/* Mensaje cuando no hay tarjetas */}
          {cards.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center text-muted-foreground">
                <div className="text-6xl mb-4">🗂️</div>
                <p className="text-lg font-medium">Tablero vacío</p>
                <p className="text-sm">
                  Haz doble clic para agregar una tarjeta
                  <br />
                  o usa el botón "Agregar Tarjeta"
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Información de estado en la esquina */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded px-2 py-1 text-xs text-muted-foreground">
        Zoom: {Math.round(zoom * 100)}% | Pan: {Math.round(pan.x)}, {Math.round(pan.y)}
      </div>
    </div>
  );
}
