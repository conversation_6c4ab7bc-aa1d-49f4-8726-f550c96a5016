import { useState, useRef, useCallback } from 'react';
import { <PERSON><PERSON> } from '../../../ui/button';
import { Textarea } from '../../../ui/textarea';
import { CorkboardCard as CorkboardCardType } from '../../../../types';
import { 
  Trash2, 
  Edit3, 
  Check, 
  X, 
  Palette,
  Image as ImageIcon,
  Link
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../../ui/dropdown-menu';

interface CorkboardCardProps {
  card: CorkboardCardType;
  isSelected: boolean;
  onUpdate: (updates: Partial<CorkboardCardType>) => void;
  onDelete: () => void;
  onSelect: () => void;
  onMove: (deltaX: number, deltaY: number) => void;
  zoom: number;
}

// Colores predefinidos para las tarjetas
const cardColors = [
  { name: 'Amar<PERSON>', value: '#fbbf24', bg: 'bg-yellow-400' },
  { name: '<PERSON><PERSON><PERSON>', value: '#f97316', bg: 'bg-orange-500' },
  { name: '<PERSON><PERSON><PERSON>', value: '#ef4444', bg: 'bg-red-500' },
  { name: 'Rosa', value: '#ec4899', bg: 'bg-pink-500' },
  { name: 'Púrpura', value: '#8b5cf6', bg: 'bg-purple-500' },
  { name: 'Azul', value: '#3b82f6', bg: 'bg-blue-500' },
  { name: 'Cian', value: '#06b6d4', bg: 'bg-cyan-500' },
  { name: 'Verde', value: '#10b981', bg: 'bg-emerald-500' },
  { name: 'Lima', value: '#84cc16', bg: 'bg-lime-500' },
  { name: 'Gris', value: '#6b7280', bg: 'bg-gray-500' },
];

export function CorkboardCard({
  card,
  isSelected,
  onUpdate,
  onDelete,
  onSelect,
  onMove,
  zoom,
}: CorkboardCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(card.content);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);

  // Manejar inicio de arrastre
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (isEditing) return;
    
    e.stopPropagation();
    onSelect();
    
    setIsDragging(true);
    setDragStart({
      x: e.clientX,
      y: e.clientY,
    });
  }, [isEditing, onSelect]);

  // Manejar arrastre
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaX = (e.clientX - dragStart.x) / zoom;
    const deltaY = (e.clientY - dragStart.y) / zoom;

    onMove(deltaX, deltaY);
    
    setDragStart({
      x: e.clientX,
      y: e.clientY,
    });
  }, [isDragging, dragStart, zoom, onMove]);

  // Finalizar arrastre
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Manejar edición
  const handleStartEdit = useCallback(() => {
    setIsEditing(true);
    setEditContent(card.content);
  }, [card.content]);

  const handleSaveEdit = useCallback(() => {
    onUpdate({ content: editContent });
    setIsEditing(false);
  }, [editContent, onUpdate]);

  const handleCancelEdit = useCallback(() => {
    setEditContent(card.content);
    setIsEditing(false);
  }, [card.content]);

  // Manejar cambio de color
  const handleColorChange = useCallback((color: string) => {
    onUpdate({ color });
  }, [onUpdate]);

  // Manejar doble clic para editar
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      handleStartEdit();
    }
  }, [isEditing, handleStartEdit]);

  return (
    <div
      ref={cardRef}
      className={`absolute select-none transition-all duration-200 ${
        isDragging ? 'cursor-grabbing' : 'cursor-grab'
      } ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}`}
      style={{
        left: card.x,
        top: card.y,
        width: card.width,
        height: card.height,
        transform: isDragging ? 'rotate(2deg)' : 'rotate(0deg)',
        zIndex: isDragging ? 1000 : isSelected ? 100 : 1,
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onDoubleClick={handleDoubleClick}
    >
      {/* Tarjeta principal */}
      <div
        className="w-full h-full rounded-lg shadow-lg border-2 border-white relative overflow-hidden"
        style={{ backgroundColor: card.color }}
      >
        {/* Imagen de fondo si existe */}
        {card.imageUrl && (
          <img
            src={card.imageUrl}
            alt=""
            className="absolute inset-0 w-full h-full object-cover opacity-30"
          />
        )}

        {/* Contenido de la tarjeta */}
        <div className="relative w-full h-full p-3 flex flex-col">
          {/* Contenido editable */}
          {isEditing ? (
            <div className="flex-1 flex flex-col">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="flex-1 resize-none border-none bg-white/80 text-sm"
                placeholder="Escribe el contenido de la tarjeta..."
                autoFocus
              />
              <div className="flex gap-1 mt-2">
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  className="h-6 px-2"
                >
                  <Check className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCancelEdit}
                  className="h-6 px-2"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-hidden">
              <div className="text-sm text-gray-800 whitespace-pre-wrap break-words h-full overflow-auto">
                {card.content || 'Tarjeta vacía'}
              </div>
            </div>
          )}

          {/* Controles de la tarjeta (solo visible al hacer hover o seleccionar) */}
          {!isEditing && (isSelected || isDragging) && (
            <div className="absolute top-1 right-1 flex gap-1 opacity-90">
              {/* Menú de color */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-6 w-6 p-0"
                  >
                    <Palette className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <div className="grid grid-cols-5 gap-1 p-2">
                    {cardColors.map((color) => (
                      <button
                        key={color.value}
                        className={`w-6 h-6 rounded ${color.bg} border-2 ${
                          card.color === color.value ? 'border-gray-800' : 'border-white'
                        }`}
                        onClick={() => handleColorChange(color.value)}
                        title={color.name}
                      />
                    ))}
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Editar */}
              <Button
                size="sm"
                variant="secondary"
                onClick={handleStartEdit}
                className="h-6 w-6 p-0"
              >
                <Edit3 className="h-3 w-3" />
              </Button>

              {/* Eliminar */}
              <Button
                size="sm"
                variant="destructive"
                onClick={onDelete}
                className="h-6 w-6 p-0"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          )}

          {/* Indicador de imagen */}
          {card.imageUrl && !isEditing && (
            <div className="absolute bottom-1 left-1">
              <ImageIcon className="h-3 w-3 text-white/70" />
            </div>
          )}

          {/* Indicador de documento vinculado */}
          {card.documentId && !isEditing && (
            <div className="absolute bottom-1 right-1">
              <Link className="h-3 w-3 text-white/70" />
            </div>
          )}
        </div>

        {/* Sombra de arrastre */}
        {isDragging && (
          <div className="absolute inset-0 bg-black/10 rounded-lg pointer-events-none" />
        )}
      </div>
    </div>
  );
}
