import { useState } from 'react';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { WorkspaceInstance, WorkspaceModeType } from '../../types';
import {
  Plus,
  // X, // No usado por ahora
  MoreHorizontal,
  Edit,
  Trash2,
  FileText,
  Layers,
  BarChart3,
  Clock,
  List,
  Users,
  Map,
} from 'lucide-react';

interface WorkspaceInstanceTabsProps {
  instances: WorkspaceInstance[];
  activeInstanceId: string | null;
  onSelectInstance: (instanceId: string) => void;
  onCreateInstance: (type: WorkspaceModeType, name: string) => Promise<WorkspaceInstance | null>;
  onDeleteInstance: (instanceId: string) => Promise<boolean>;
  onRenameInstance: (instanceId: string, newName: string) => Promise<boolean>;
}

// Configuración de iconos y colores por tipo de workspace
const workspaceConfig = {
  editor: { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-50' },
  corkboard: { icon: Layers, color: 'text-orange-600', bgColor: 'bg-orange-50' },
  canvas: { icon: BarChart3, color: 'text-purple-600', bgColor: 'bg-purple-50' },
  timeline: { icon: Clock, color: 'text-green-600', bgColor: 'bg-green-50' },
  outline: { icon: List, color: 'text-indigo-600', bgColor: 'bg-indigo-50' },
  character: { icon: Users, color: 'text-pink-600', bgColor: 'bg-pink-50' },
  world: { icon: Map, color: 'text-teal-600', bgColor: 'bg-teal-50' },
};

// Templates predefinidos para cada tipo
const workspaceTemplates = {
  corkboard: [
    { name: 'Moodboard Personajes', description: 'Tablero para organizar personajes' },
    { name: 'Moodboard Ambientes', description: 'Tablero para ambientes y locaciones' },
    { name: 'Moodboard Ideas', description: 'Tablero general de ideas' },
    { name: 'Tablero Personalizado', description: 'Tablero en blanco' },
  ],
  canvas: [
    { name: 'Mapa Mental', description: 'Diagrama de ideas conectadas' },
    { name: 'Diagrama de Relaciones', description: 'Conexiones entre personajes' },
    { name: 'Esquema de Trama', description: 'Estructura visual de la historia' },
    { name: 'Canvas Personalizado', description: 'Lienzo en blanco' },
  ],
  timeline: [
    { name: 'Cronología Principal', description: 'Línea de tiempo de la historia' },
    { name: 'Timeline de Personaje', description: 'Cronología de un personaje específico' },
    { name: 'Eventos Históricos', description: 'Contexto histórico del mundo' },
    { name: 'Timeline Personalizado', description: 'Cronología personalizada' },
  ],
};

export function WorkspaceInstanceTabs({
  instances,
  activeInstanceId,
  onSelectInstance,
  onCreateInstance,
  onDeleteInstance,
  onRenameInstance,
}: WorkspaceInstanceTabsProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [selectedType, setSelectedType] = useState<WorkspaceModeType>('corkboard');
  const [newInstanceName, setNewInstanceName] = useState('');
  const [renameInstanceId, setRenameInstanceId] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);

  // Manejar creación de nueva instancia
  const handleCreateInstance = async () => {
    if (!newInstanceName.trim()) return;

    setIsCreating(true);
    try {
      const result = await onCreateInstance(selectedType, newInstanceName.trim());
      if (result) {
        setShowCreateDialog(false);
        setNewInstanceName('');
        setSelectedType('corkboard');
      }
    } catch (error) {
      console.error('Error creating instance:', error);
    } finally {
      setIsCreating(false);
    }
  };

  // Manejar renombrado de instancia
  const handleRenameInstance = async () => {
    if (!renameInstanceId || !newInstanceName.trim()) return;

    setIsRenaming(true);
    try {
      const result = await onRenameInstance(renameInstanceId, newInstanceName.trim());
      if (result) {
        setShowRenameDialog(false);
        setRenameInstanceId(null);
        setNewInstanceName('');
      }
    } catch (error) {
      console.error('Error renaming instance:', error);
    } finally {
      setIsRenaming(false);
    }
  };

  // Abrir diálogo de renombrado
  const openRenameDialog = (instance: WorkspaceInstance) => {
    setRenameInstanceId(instance.id);
    setNewInstanceName(instance.name);
    setShowRenameDialog(true);
  };

  // Usar template predefinido
  const useTemplate = (template: { name: string; description: string }) => {
    setNewInstanceName(template.name);
  };

  return (
    <>
      {/* Barra de pestañas */}
      <div className="flex items-center border-b bg-background">
        <div className="flex items-center flex-1 overflow-x-auto">
          {instances.map((instance) => {
            const config = workspaceConfig[instance.type];
            const Icon = config.icon;
            const isActive = instance.id === activeInstanceId;

            return (
              <div
                key={instance.id}
                className={`flex items-center gap-2 px-3 py-2 border-r cursor-pointer transition-colors ${
                  isActive
                    ? 'bg-accent border-b-2 border-b-primary'
                    : 'hover:bg-accent/50'
                }`}
                onClick={() => onSelectInstance(instance.id)}
              >
                <Icon className={`h-4 w-4 ${config.color}`} />
                <span className="text-sm font-medium truncate max-w-32">
                  {instance.name}
                </span>
                
                {/* Menú contextual */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:opacity-100"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => openRenameDialog(instance)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Renombrar
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onDeleteInstance(instance.id)}
                      className="text-red-600"
                      disabled={instances.length <= 1}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Eliminar
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          })}
        </div>

        {/* Botón para crear nueva instancia */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 mx-2"
          onClick={() => setShowCreateDialog(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Diálogo para crear nueva instancia */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Crear Nuevo Workspace</DialogTitle>
            <DialogDescription>
              Elige el tipo de workspace y dale un nombre descriptivo.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Selector de tipo */}
            <div>
              <Label htmlFor="workspace-type">Tipo de Workspace</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {Object.entries(workspaceConfig).map(([type, config]) => {
                  const Icon = config.icon;
                  const isSelected = selectedType === type;
                  
                  return (
                    <button
                      key={type}
                      type="button"
                      className={`flex items-center gap-2 p-3 rounded-lg border transition-colors ${
                        isSelected
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:bg-accent'
                      }`}
                      onClick={() => setSelectedType(type as WorkspaceModeType)}
                    >
                      <Icon className={`h-4 w-4 ${config.color}`} />
                      <span className="text-sm font-medium capitalize">{type}</span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Templates predefinidos */}
            {workspaceTemplates[selectedType as keyof typeof workspaceTemplates] && (
              <div>
                <Label>Templates Predefinidos</Label>
                <div className="grid gap-2 mt-2">
                  {workspaceTemplates[selectedType as keyof typeof workspaceTemplates].map((template) => (
                    <button
                      key={template.name}
                      type="button"
                      className="flex flex-col items-start p-2 rounded border hover:bg-accent text-left"
                      onClick={() => useTemplate(template)}
                    >
                      <span className="text-sm font-medium">{template.name}</span>
                      <span className="text-xs text-muted-foreground">{template.description}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Nombre de la instancia */}
            <div>
              <Label htmlFor="instance-name">Nombre del Workspace</Label>
              <Input
                id="instance-name"
                value={newInstanceName}
                onChange={(e) => setNewInstanceName(e.target.value)}
                placeholder="Ej: Moodboard Personajes"
                className="mt-1"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={isCreating}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleCreateInstance}
              disabled={!newInstanceName.trim() || isCreating}
            >
              {isCreating ? 'Creando...' : 'Crear Workspace'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo para renombrar instancia */}
      <Dialog open={showRenameDialog} onOpenChange={setShowRenameDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Renombrar Workspace</DialogTitle>
            <DialogDescription>
              Cambia el nombre de este workspace.
            </DialogDescription>
          </DialogHeader>

          <div>
            <Label htmlFor="rename-instance">Nuevo Nombre</Label>
            <Input
              id="rename-instance"
              value={newInstanceName}
              onChange={(e) => setNewInstanceName(e.target.value)}
              placeholder="Nuevo nombre del workspace"
              className="mt-1"
            />
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRenameDialog(false)}
              disabled={isRenaming}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleRenameInstance}
              disabled={!newInstanceName.trim() || isRenaming}
            >
              {isRenaming ? 'Renombrando...' : 'Renombrar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
