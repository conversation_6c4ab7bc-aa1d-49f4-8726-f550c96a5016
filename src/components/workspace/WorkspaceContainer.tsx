import { useWorkspaces } from '../../hooks/useWorkspaces';
import { WorkspaceInstanceTabs } from './WorkspaceInstanceTabs';
import { EditorMode } from './modes/EditorMode';
import { CorkboardMode } from './modes/CorkboardMode';
// import { WorkspaceInstance } from '../../types'; // Usado implícitamente
import { FileText, AlertCircle, Loader2 } from 'lucide-react';

interface WorkspaceContainerProps {
  documentId: number | null;
}

export function WorkspaceContainer({ documentId }: WorkspaceContainerProps) {
  const {
    instances,
    activeInstanceId,
    activeInstance,
    isLoading,
    error,
    createInstance,
    deleteInstance,
    setActiveInstance,
    updateInstanceData,
    renameInstance,
  } = useWorkspaces(documentId);

  // Renderizar el workspace activo basado en su tipo
  const renderActiveWorkspace = () => {
    if (!activeInstance) {
      return (
        <div className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No hay workspace activo</p>
            <p className="text-sm">Selecciona o crea un workspace para comenzar</p>
          </div>
        </div>
      );
    }

    // Por ahora solo soportamos el modo editor
    // Los otros modos se implementarán en los siguientes hitos
    switch (activeInstance.type) {
      case 'editor':
        return (
          <EditorMode
            instance={activeInstance}
            onUpdateData={(data) => updateInstanceData(activeInstance.id, data)}
          />
        );
      
      case 'corkboard':
        return (
          <CorkboardMode
            instance={activeInstance}
            onUpdateData={(data) => updateInstanceData(activeInstance.id, data)}
          />
        );
      
      case 'canvas':
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="h-12 w-12 mx-auto mb-4 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">📊</span>
              </div>
              <p className="text-lg font-medium">Canvas Mode</p>
              <p className="text-sm">Se implementará en el Hito 3</p>
            </div>
          </div>
        );
      
      case 'timeline':
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="h-12 w-12 mx-auto mb-4 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">⏱️</span>
              </div>
              <p className="text-lg font-medium">Timeline Mode</p>
              <p className="text-sm">Se implementará en el Hito 4</p>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Tipo de workspace no soportado</p>
              <p className="text-sm">Tipo: {activeInstance.type}</p>
            </div>
          </div>
        );
    }
  };

  // Mostrar loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
          <p className="text-sm text-muted-foreground">Cargando workspaces...</p>
        </div>
      </div>
    );
  }

  // Mostrar error
  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-lg font-medium text-red-600">Error</p>
          <p className="text-sm text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  // Si no hay documento seleccionado
  if (!documentId) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Selecciona un documento</p>
          <p className="text-sm">Elige un documento del explorador para comenzar a trabajar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Pestañas de instancias */}
      <WorkspaceInstanceTabs
        instances={instances}
        activeInstanceId={activeInstanceId}
        onSelectInstance={setActiveInstance}
        onCreateInstance={createInstance}
        onDeleteInstance={deleteInstance}
        onRenameInstance={renameInstance}
      />

      {/* Contenido del workspace activo */}
      <div className="flex-1 overflow-hidden">
        {renderActiveWorkspace()}
      </div>
    </div>
  );
}
