import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { Button } from './ui/button';
import { 
  BarChart3, 
  FileText, 
  Folder, 
  Target, 
  Calendar,
  Clock,
  TrendingUp
} from 'lucide-react';
import { Document } from '../types';

interface ProjectStatsProps {
  documents: Document[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ProjectStatistics {
  totalDocuments: number;
  totalFolders: number;
  totalWords: number;
  totalCharacters: number;
  documentsInCompile: number;
  averageWordsPerDocument: number;
  lastModified: string;
  creationDate: string;
  dailyWordGoal: number;
  wordsToday: number;
}

export function ProjectStats({ documents, isOpen, onOpenChange }: ProjectStatsProps) {
  const [stats, setStats] = useState<ProjectStatistics | null>(null);

  useEffect(() => {
    if (documents.length > 0) {
      calculateStats();
    }
  }, [documents]);

  const calculateStats = () => {
    const totalDocuments = documents.filter(doc => !doc.is_folder).length;
    const totalFolders = documents.filter(doc => doc.is_folder).length;
    const documentsInCompile = documents.filter(doc => doc.include_in_compile).length;

    let totalWords = 0;
    let totalCharacters = 0;
    let lastModified = '';
    let creationDate = '';

    documents.forEach(doc => {
      if (!doc.is_folder && doc.content) {
        // Remover HTML tags y contar palabras
        const text = doc.content.replace(/<[^>]*>/g, '');
        const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        totalWords += words;
        totalCharacters += text.length;
      }

      // Encontrar fechas más recientes y más antiguas
      if (!lastModified || doc.updated_at > lastModified) {
        lastModified = doc.updated_at;
      }
      if (!creationDate || doc.created_at < creationDate) {
        creationDate = doc.created_at;
      }
    });

    const averageWordsPerDocument = totalDocuments > 0 ? Math.round(totalWords / totalDocuments) : 0;

    // Datos simulados para objetivos diarios
    const dailyWordGoal = 1000;
    const wordsToday = Math.floor(Math.random() * 800); // Simulado

    setStats({
      totalDocuments,
      totalFolders,
      totalWords,
      totalCharacters,
      documentsInCompile,
      averageWordsPerDocument,
      lastModified,
      creationDate,
      dailyWordGoal,
      wordsToday,
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getProgressPercentage = (current: number, goal: number) => {
    return Math.min((current / goal) * 100, 100);
  };

  if (!stats) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Estadísticas del Proyecto
          </DialogTitle>
          <DialogDescription>
            Resumen completo del progreso y métricas de tu proyecto de escritura.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Estadísticas generales */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Documentos</span>
                </div>
                <span className="text-lg font-bold">{stats.totalDocuments}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <Folder className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">Carpetas</span>
                </div>
                <span className="text-lg font-bold">{stats.totalFolders}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium">En compilación</span>
                </div>
                <span className="text-lg font-bold">{stats.documentsInCompile}</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-purple-500" />
                    <span className="text-sm font-medium">Palabras totales</span>
                  </div>
                </div>
                <span className="text-lg font-bold">{stats.totalWords.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div>
                  <span className="text-sm font-medium">Caracteres</span>
                </div>
                <span className="text-lg font-bold">{stats.totalCharacters.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                <div>
                  <span className="text-sm font-medium">Promedio/doc</span>
                </div>
                <span className="text-lg font-bold">{stats.averageWordsPerDocument}</span>
              </div>
            </div>
          </div>

          {/* Objetivo diario */}
          <div className="space-y-3">
            <h3 className="text-sm font-semibold">Objetivo Diario</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Progreso de hoy</span>
                <span>{stats.wordsToday} / {stats.dailyWordGoal} palabras</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage(stats.wordsToday, stats.dailyWordGoal)}%` }}
                />
              </div>
              <div className="text-xs text-muted-foreground">
                {stats.dailyWordGoal - stats.wordsToday > 0 
                  ? `Faltan ${stats.dailyWordGoal - stats.wordsToday} palabras para completar el objetivo`
                  : '¡Objetivo completado!'
                }
              </div>
            </div>
          </div>

          {/* Fechas importantes */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Calendar className="h-4 w-4" />
                Proyecto creado
              </div>
              <p className="text-sm text-muted-foreground">
                {formatDate(stats.creationDate)}
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Clock className="h-4 w-4" />
                Última modificación
              </div>
              <p className="text-sm text-muted-foreground">
                {formatDate(stats.lastModified)}
              </p>
            </div>
          </div>

          {/* Estimaciones */}
          <div className="space-y-3 border-t pt-4">
            <h3 className="text-sm font-semibold">Estimaciones</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Páginas estimadas:</span>
                <span className="ml-2 font-medium">{Math.ceil(stats.totalWords / 250)}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Tiempo de lectura:</span>
                <span className="ml-2 font-medium">{Math.ceil(stats.totalWords / 200)} min</span>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
