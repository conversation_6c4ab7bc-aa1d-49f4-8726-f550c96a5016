import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Label } from './ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import {
  FileText,
  Image,
  Tag,
  Plus,
  X,
  Upload,
  BarChart3,
  Calendar,
  Clock,
} from 'lucide-react';
import { Document, Tag as TagType } from '../types';

interface MetadataPanelProps {
  documentId: number | null;
}

// Datos de ejemplo
const mockDocuments: Record<number, Document> = {
  2: {
    id: 2,
    title: 'Capítulo 1: El Comienzo',
    content: 'Era una noche oscura y tormentosa...',
    synopsis: 'El protagonista descubre su destino cuando encuentra una misteriosa carta en el ático de su abuela. Esta carta revela secretos sobre su linaje y un poder ancestral que debe aprender a controlar.',
    order_index: 0,
    parent_id: 1,
    is_folder: false,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
    include_in_compile: true,
    compile_content: true,
    compile_title: true,
    compile_synopsis: false,
  },
  3: {
    id: 3,
    title: 'Capítulo 2: La Aventura',
    content: 'El viaje comenzó al amanecer...',
    synopsis: 'El protagonista emprende su primer viaje fuera del pueblo natal, enfrentándose a desafíos que pondrán a prueba su determinación y las nuevas habilidades que está descubriendo.',
    order_index: 1,
    parent_id: 1,
    is_folder: false,
    created_at: '2024-01-02T09:15:00Z',
    updated_at: '2024-01-16T11:45:00Z',
    include_in_compile: true,
    compile_content: true,
    compile_title: true,
    compile_synopsis: false,
  },
  5: {
    id: 5,
    title: 'Protagonista',
    content: 'Alex Meridian, 17 años...',
    synopsis: 'Perfil completo del personaje principal de la historia, incluyendo su trasfondo, motivaciones y arco de desarrollo.',
    order_index: 0,
    parent_id: 4,
    is_folder: false,
    created_at: '2024-01-01T08:00:00Z',
    updated_at: '2024-01-10T16:20:00Z',
    include_in_compile: false,
    compile_content: false,
    compile_title: false,
    compile_synopsis: false,
  },
};

const mockTags: TagType[] = [
  { id: 1, name: 'Acción', color: '#ef4444' },
  { id: 2, name: 'Misterio', color: '#8b5cf6' },
  { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
  { id: 4, name: 'Desarrollo', color: '#10b981' },
];

const mockDocumentTags: Record<number, number[]> = {
  2: [1, 2],
  3: [1],
  5: [3, 4],
};

export function MetadataPanel({ documentId }: MetadataPanelProps) {
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [synopsis, setSynopsis] = useState('');
  const [documentTags, setDocumentTags] = useState<number[]>([]);
  const [isNewTagOpen, setIsNewTagOpen] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#3b82f6');

  // Cargar documento cuando cambia el ID
  useEffect(() => {
    if (documentId && mockDocuments[documentId]) {
      const document = mockDocuments[documentId];
      setCurrentDocument(document);
      setSynopsis(document.synopsis || '');
      setDocumentTags(mockDocumentTags[documentId] || []);
    } else {
      setCurrentDocument(null);
      setSynopsis('');
      setDocumentTags([]);
    }
  }, [documentId]);

  const handleSynopsisChange = (value: string) => {
    setSynopsis(value);
    // TODO: Implementar auto-guardado
    console.log('Synopsis actualizada:', value);
  };

  const handleAddTag = (tagId: number) => {
    if (!documentTags.includes(tagId)) {
      const newTags = [...documentTags, tagId];
      setDocumentTags(newTags);
      console.log('Etiqueta añadida:', tagId);
      // TODO: Guardar en base de datos
    }
  };

  const handleRemoveTag = (tagId: number) => {
    const newTags = documentTags.filter(id => id !== tagId);
    setDocumentTags(newTags);
    console.log('Etiqueta eliminada:', tagId);
    // TODO: Guardar en base de datos
  };

  const handleCreateTag = () => {
    if (newTagName.trim()) {
      console.log('Crear nueva etiqueta:', newTagName, newTagColor);
      // TODO: Implementar creación de etiqueta
      setNewTagName('');
      setNewTagColor('#3b82f6');
      setIsNewTagOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getWordCount = (content: string) => {
    // Remover HTML tags y contar palabras
    const text = content.replace(/<[^>]*>/g, '');
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  if (!documentId || !currentDocument) {
    return (
      <div className="flex flex-col h-full p-4">
        <div className="flex items-center justify-center flex-1 text-muted-foreground">
          <div className="text-center">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Selecciona un documento para ver sus metadatos</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Encabezado */}
      <div className="p-3 border-b">
        <h2 className="text-sm font-semibold">Metadatos</h2>
      </div>

      <div className="flex-1 overflow-auto p-3 space-y-4">
        {/* Información del documento */}
        <div className="space-y-2">
          <Label className="text-xs font-medium text-muted-foreground">INFORMACIÓN</Label>
          <div className="space-y-1 text-xs">
            <div className="flex items-center gap-2">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              <span>Creado: {formatDate(currentDocument.created_at)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span>Modificado: {formatDate(currentDocument.updated_at)}</span>
            </div>
          </div>
        </div>

        {/* Estadísticas */}
        {!currentDocument.is_folder && (
          <div className="space-y-2">
            <Label className="text-xs font-medium text-muted-foreground">ESTADÍSTICAS</Label>
            <div className="space-y-1 text-xs">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-3 w-3 text-muted-foreground" />
                <span>{getWordCount(currentDocument.content || '')} palabras</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-3 w-3 text-muted-foreground" />
                <span>{(currentDocument.content || '').length} caracteres</span>
              </div>
            </div>
          </div>
        )}

        {/* Sinopsis */}
        <div className="space-y-2">
          <Label htmlFor="synopsis" className="text-xs font-medium text-muted-foreground">
            SINOPSIS
          </Label>
          <Textarea
            id="synopsis"
            value={synopsis}
            onChange={(e) => handleSynopsisChange(e.target.value)}
            placeholder="Describe brevemente el contenido de este documento..."
            className="min-h-[80px] text-xs"
          />
        </div>

        {/* Etiquetas */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-muted-foreground">ETIQUETAS</Label>
            <Dialog open={isNewTagOpen} onOpenChange={setIsNewTagOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
                  <Plus className="h-3 w-3" />
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Nueva Etiqueta</DialogTitle>
                  <DialogDescription>
                    Crea una nueva etiqueta para organizar tus documentos.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tag-name" className="text-right">
                      Nombre
                    </Label>
                    <Input
                      id="tag-name"
                      value={newTagName}
                      onChange={(e) => setNewTagName(e.target.value)}
                      className="col-span-3"
                      placeholder="Nombre de la etiqueta"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tag-color" className="text-right">
                      Color
                    </Label>
                    <Input
                      id="tag-color"
                      type="color"
                      value={newTagColor}
                      onChange={(e) => setNewTagColor(e.target.value)}
                      className="col-span-3 h-10"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    type="submit" 
                    onClick={handleCreateTag}
                    disabled={!newTagName.trim()}
                  >
                    Crear Etiqueta
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Etiquetas asignadas */}
          <div className="space-y-1">
            {documentTags.map(tagId => {
              const tag = mockTags.find(t => t.id === tagId);
              if (!tag) return null;
              
              return (
                <div
                  key={tag.id}
                  className="flex items-center justify-between p-1 rounded text-xs"
                  style={{ backgroundColor: tag.color + '20', color: tag.color }}
                >
                  <span className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {tag.name}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-transparent"
                    onClick={() => handleRemoveTag(tag.id)}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </div>
              );
            })}
          </div>

          {/* Etiquetas disponibles */}
          <div className="space-y-1">
            <Label className="text-xs text-muted-foreground">Disponibles:</Label>
            <div className="flex flex-wrap gap-1">
              {mockTags
                .filter(tag => !documentTags.includes(tag.id))
                .map(tag => (
                  <Button
                    key={tag.id}
                    variant="outline"
                    size="sm"
                    className="h-6 text-xs"
                    style={{ borderColor: tag.color, color: tag.color }}
                    onClick={() => handleAddTag(tag.id)}
                  >
                    {tag.name}
                  </Button>
                ))}
            </div>
          </div>
        </div>

        {/* Imágenes */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium text-muted-foreground">IMÁGENES</Label>
            <Button variant="ghost" size="sm" className="h-5 w-5 p-0">
              <Upload className="h-3 w-3" />
            </Button>
          </div>
          <div className="text-xs text-muted-foreground text-center py-4">
            No hay imágenes asociadas
          </div>
        </div>

        {/* Configuración de compilación */}
        <div className="space-y-2">
          <Label className="text-xs font-medium text-muted-foreground">COMPILACIÓN</Label>
          <div className="space-y-1">
            <label className="flex items-center space-x-2 text-xs">
              <input
                type="checkbox"
                checked={currentDocument.include_in_compile}
                onChange={(e) => console.log('Include in compile:', e.target.checked)}
                className="rounded"
              />
              <span>Incluir en compilación</span>
            </label>
            <label className="flex items-center space-x-2 text-xs">
              <input
                type="checkbox"
                checked={currentDocument.compile_title}
                onChange={(e) => console.log('Compile title:', e.target.checked)}
                className="rounded"
              />
              <span>Incluir título</span>
            </label>
            <label className="flex items-center space-x-2 text-xs">
              <input
                type="checkbox"
                checked={currentDocument.compile_content}
                onChange={(e) => console.log('Compile content:', e.target.checked)}
                className="rounded"
              />
              <span>Incluir contenido</span>
            </label>
            <label className="flex items-center space-x-2 text-xs">
              <input
                type="checkbox"
                checked={currentDocument.compile_synopsis}
                onChange={(e) => console.log('Compile synopsis:', e.target.checked)}
                className="rounded"
              />
              <span>Incluir sinopsis</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
