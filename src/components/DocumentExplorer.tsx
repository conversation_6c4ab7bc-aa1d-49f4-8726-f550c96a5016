import { useState } from 'react';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import {
  FileText,
  Folder,
  FolderOpen,
  ChevronRight,
  ChevronDown,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
} from 'lucide-react';
import { Document, DocumentTreeNode } from '../types';

interface DocumentExplorerProps {
  onSelectDocument: (documentId: number | null) => void;
  selectedDocumentId: number | null;
}

// Datos de ejemplo para desarrollo
const mockDocuments: DocumentTreeNode[] = [
  {
    id: 1,
    title: 'Mi Novela',
    content: '',
    synopsis: 'Una historia épica sobre...',
    order_index: 0,
    is_folder: true,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    include_in_compile: true,
    compile_content: true,
    compile_title: true,
    compile_synopsis: false,
    level: 0,
    isExpanded: true,
    hasChildren: true,
    children: [
      {
        id: 2,
        title: 'Capítulo 1: El Comienzo',
        content: 'Era una noche oscura y tormentosa...',
        synopsis: 'El protagonista descubre su destino',
        order_index: 0,
        parent_id: 1,
        is_folder: false,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        include_in_compile: true,
        compile_content: true,
        compile_title: true,
        compile_synopsis: false,
        level: 1,
        hasChildren: false,
      },
      {
        id: 3,
        title: 'Capítulo 2: La Aventura',
        content: 'El viaje comenzó al amanecer...',
        synopsis: 'Primeros desafíos del héroe',
        order_index: 1,
        parent_id: 1,
        is_folder: false,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        include_in_compile: true,
        compile_content: true,
        compile_title: true,
        compile_synopsis: false,
        level: 1,
        hasChildren: false,
      },
    ]
  },
  {
    id: 4,
    title: 'Personajes',
    content: '',
    synopsis: 'Notas sobre los personajes principales',
    order_index: 1,
    is_folder: true,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    include_in_compile: false,
    compile_content: false,
    compile_title: false,
    compile_synopsis: false,
    level: 0,
    isExpanded: false,
    hasChildren: true,
    children: [
      {
        id: 5,
        title: 'Protagonista',
        content: 'Descripción del personaje principal...',
        synopsis: 'Héroe de la historia',
        order_index: 0,
        parent_id: 4,
        is_folder: false,
        created_at: '2024-01-01',
        updated_at: '2024-01-01',
        include_in_compile: false,
        compile_content: false,
        compile_title: false,
        compile_synopsis: false,
        level: 1,
        hasChildren: false,
      },
    ]
  },
];

export function DocumentExplorer({ onSelectDocument, selectedDocumentId }: DocumentExplorerProps) {
  const [documents, setDocuments] = useState<DocumentTreeNode[]>(mockDocuments);

  const toggleExpanded = (documentId: number) => {
    setDocuments(prev => 
      prev.map(doc => 
        doc.id === documentId 
          ? { ...doc, isExpanded: !doc.isExpanded }
          : doc
      )
    );
  };

  const handleSelectDocument = (document: DocumentTreeNode) => {
    if (!document.is_folder) {
      onSelectDocument(document.id);
    }
  };

  const handleNewDocument = (parentId?: number) => {
    console.log('Crear nuevo documento', parentId);
    // TODO: Implementar creación de documento
  };

  const handleNewFolder = (parentId?: number) => {
    console.log('Crear nueva carpeta', parentId);
    // TODO: Implementar creación de carpeta
  };

  const handleEditDocument = (documentId: number) => {
    console.log('Editar documento', documentId);
    // TODO: Implementar edición de documento
  };

  const handleDeleteDocument = (documentId: number) => {
    console.log('Eliminar documento', documentId);
    // TODO: Implementar eliminación de documento
  };

  const renderDocument = (document: DocumentTreeNode) => {
    const isSelected = selectedDocumentId === document.id;
    const indentLevel = document.level * 16;

    return (
      <div key={document.id}>
        <div
          className={`flex items-center gap-1 px-2 py-1 hover:bg-accent cursor-pointer ${
            isSelected ? 'bg-accent' : ''
          }`}
          style={{ paddingLeft: `${8 + indentLevel}px` }}
          onClick={() => handleSelectDocument(document)}
        >
          {/* Botón de expansión */}
          {document.hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(document.id);
              }}
            >
              {document.isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </Button>
          )}
          
          {/* Icono del documento */}
          <div className="flex-shrink-0">
            {document.is_folder ? (
              document.isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-500" />
              ) : (
                <Folder className="h-4 w-4 text-blue-500" />
              )
            ) : (
              <FileText className="h-4 w-4 text-gray-500" />
            )}
          </div>

          {/* Título del documento */}
          <span className="flex-1 text-sm truncate">
            {document.title}
          </span>

          {/* Menú de opciones */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleNewDocument(document.id)}>
                <FileText className="mr-2 h-4 w-4" />
                Nuevo Documento
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleNewFolder(document.id)}>
                <Folder className="mr-2 h-4 w-4" />
                Nueva Carpeta
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleEditDocument(document.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Renombrar
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleDeleteDocument(document.id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Documentos hijos */}
        {document.isExpanded && document.children && (
          <div>
            {document.children.map(child => renderDocument(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Encabezado */}
      <div className="flex items-center justify-between p-2 border-b">
        <h2 className="text-sm font-semibold">Documentos</h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleNewDocument()}>
              <FileText className="mr-2 h-4 w-4" />
              Nuevo Documento
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleNewFolder()}>
              <Folder className="mr-2 h-4 w-4" />
              Nueva Carpeta
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Lista de documentos */}
      <div className="flex-1 overflow-auto">
        {documents.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            No hay documentos en este proyecto
          </div>
        ) : (
          <div className="group">
            {documents.map(document => renderDocument(document))}
          </div>
        )}
      </div>
    </div>
  );
}
