import { useState, useCallback } from 'react';

export interface ToastMessage {
  id: string;
  title?: string;
  description: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
}

export function useToast() {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const addToast = useCallback((toast: Omit<ToastMessage, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastMessage = {
      id,
      duration: 5000,
      ...toast,
    };

    setToasts(prev => [...prev, newToast]);

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const removeAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods
  const toast = useCallback((description: string, options?: Partial<ToastMessage>) => {
    return addToast({ description, ...options });
  }, [addToast]);

  const success = useCallback((description: string, title?: string) => {
    return addToast({ description, title, variant: 'success' });
  }, [addToast]);

  const error = useCallback((description: string, title?: string) => {
    return addToast({ description, title, variant: 'destructive' });
  }, [addToast]);

  const warning = useCallback((description: string, title?: string) => {
    return addToast({ description, title, variant: 'warning' });
  }, [addToast]);

  const info = useCallback((description: string, title?: string) => {
    return addToast({ description, title, variant: 'default' });
  }, [addToast]);

  return {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    toast,
    success,
    error,
    warning,
    info,
  };
}
