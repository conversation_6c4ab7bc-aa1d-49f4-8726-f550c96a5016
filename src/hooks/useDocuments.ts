import { useState, useEffect } from 'react';
import { Document, DocumentTreeNode } from '../types';

// Hook para manejar documentos
export function useDocuments() {
  const [documents, setDocuments] = useState<DocumentTreeNode[]>([]);
  const [selectedDocumentId, setSelectedDocumentId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Datos de ejemplo
  const mockDocuments: DocumentTreeNode[] = [
    {
      id: 1,
      title: 'Mi Novela',
      content: '',
      synopsis: 'Una historia épica sobre...',
      order_index: 0,
      is_folder: true,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      include_in_compile: true,
      compile_content: true,
      compile_title: true,
      compile_synopsis: false,
      level: 0,
      isExpanded: true,
      hasChildren: true,
      children: [
        {
          id: 2,
          title: 'Capítulo 1: El Comienzo',
          content: 'Era una noche oscura y tormentosa...',
          synopsis: 'El protagonista descubre su destino',
          order_index: 0,
          parent_id: 1,
          is_folder: false,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          include_in_compile: true,
          compile_content: true,
          compile_title: true,
          compile_synopsis: false,
          level: 1,
          hasChildren: false,
        },
        {
          id: 3,
          title: 'Capítulo 2: La Aventura',
          content: 'El viaje comenzó al amanecer...',
          synopsis: 'Primeros desafíos del héroe',
          order_index: 1,
          parent_id: 1,
          is_folder: false,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          include_in_compile: true,
          compile_content: true,
          compile_title: true,
          compile_synopsis: false,
          level: 1,
          hasChildren: false,
        },
      ]
    },
    {
      id: 4,
      title: 'Personajes',
      content: '',
      synopsis: 'Notas sobre los personajes principales',
      order_index: 1,
      is_folder: true,
      created_at: '2024-01-01',
      updated_at: '2024-01-01',
      include_in_compile: false,
      compile_content: false,
      compile_title: false,
      compile_synopsis: false,
      level: 0,
      isExpanded: false,
      hasChildren: true,
      children: [
        {
          id: 5,
          title: 'Protagonista',
          content: 'Descripción del personaje principal...',
          synopsis: 'Héroe de la historia',
          order_index: 0,
          parent_id: 4,
          is_folder: false,
          created_at: '2024-01-01',
          updated_at: '2024-01-01',
          include_in_compile: false,
          compile_content: false,
          compile_title: false,
          compile_synopsis: false,
          level: 1,
          hasChildren: false,
        },
      ]
    },
  ];

  // Cargar documentos
  const loadDocuments = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar carga real desde base de datos
      console.log('Cargando documentos...');
      setDocuments(mockDocuments);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Crear nuevo documento
  const createDocument = async (title: string, parentId?: number, isFolder: boolean = false) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar creación real
      console.log('Creando documento:', title, parentId, isFolder);
      
      const newDocument: DocumentTreeNode = {
        id: Date.now(), // ID temporal
        title,
        content: '',
        synopsis: '',
        order_index: 0,
        parent_id: parentId,
        is_folder: isFolder,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        include_in_compile: !isFolder,
        compile_content: !isFolder,
        compile_title: !isFolder,
        compile_synopsis: false,
        level: parentId ? 1 : 0,
        hasChildren: false,
      };

      // Agregar a la lista
      setDocuments(prev => [...prev, newDocument]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Actualizar documento
  const updateDocument = async (documentId: number, updates: Partial<Document>) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar actualización real
      console.log('Actualizando documento:', documentId, updates);
      
      setDocuments(prev => 
        prev.map(doc => 
          doc.id === documentId 
            ? { ...doc, ...updates, updated_at: new Date().toISOString() }
            : doc
        )
      );
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Eliminar documento
  const deleteDocument = async (documentId: number) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar eliminación real
      console.log('Eliminando documento:', documentId);
      
      setDocuments(prev => prev.filter(doc => doc.id !== documentId));
      
      if (selectedDocumentId === documentId) {
        setSelectedDocumentId(null);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Obtener documento por ID
  const getDocumentById = (documentId: number): DocumentTreeNode | null => {
    const findDocument = (docs: DocumentTreeNode[]): DocumentTreeNode | null => {
      for (const doc of docs) {
        if (doc.id === documentId) return doc;
        if (doc.children) {
          const found = findDocument(doc.children);
          if (found) return found;
        }
      }
      return null;
    };
    
    return findDocument(documents);
  };

  // Cargar documentos al montar el componente
  useEffect(() => {
    loadDocuments();
  }, []);

  return {
    documents,
    selectedDocumentId,
    isLoading,
    error,
    setSelectedDocumentId,
    loadDocuments,
    createDocument,
    updateDocument,
    deleteDocument,
    getDocumentById,
  };
}
