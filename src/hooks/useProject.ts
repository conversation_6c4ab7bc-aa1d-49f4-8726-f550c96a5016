import { useState, useEffect } from 'react';
import { Document, Tag, Project } from '../types';

// Hook para manejar el estado del proyecto actual
export function useProject() {
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cargar proyecto
  const loadProject = async (projectPath: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar carga real desde Electron
      console.log('Cargando proyecto desde:', projectPath);
      
      // Datos de ejemplo por ahora
      const mockProject: Project = {
        name: '<PERSON> Novela',
        path: projectPath,
        documents: [],
        tags: [
          { id: 1, name: 'Acci<PERSON>', color: '#ef4444' },
          { id: 2, name: '<PERSON><PERSON>', color: '#8b5cf6' },
          { id: 3, name: '<PERSON><PERSON><PERSON> Principal', color: '#06b6d4' },
          { id: 4, name: 'Desarrollo', color: '#10b981' },
        ],
        settings: [
          { id: 1, name: 'project_name', value: 'Mi Novela' },
        ],
      };

      setCurrentProject(mockProject);
      setTags(mockProject.tags);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Crear nuevo proyecto
  const createProject = async (name: string, path: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar creación real con Electron
      console.log('Creando proyecto:', name, path);
      
      const newProject: Project = {
        name,
        path,
        documents: [],
        tags: [],
        settings: [
          { id: 1, name: 'project_name', value: name },
        ],
      };

      setCurrentProject(newProject);
      setDocuments([]);
      setTags([]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Guardar proyecto
  const saveProject = async () => {
    if (!currentProject) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar guardado real
      console.log('Guardando proyecto:', currentProject.name);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    currentProject,
    documents,
    tags,
    isLoading,
    error,
    loadProject,
    createProject,
    saveProject,
  };
}
