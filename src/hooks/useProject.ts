import { useState, useEffect } from 'react';
import { Document, Tag, Project } from '../types';

// Hook para manejar el estado del proyecto actual
export function useProject() {
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Cargar proyecto
  const loadProject = async (projectPath: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar carga real desde Electron
      console.log('Cargando proyecto desde:', projectPath);
      
      // Datos de ejemplo por ahora
      const mockProject: Project = {
        name: '<PERSON> Novela',
        path: projectPath,
        documents: [],
        tags: [
          { id: 1, name: 'Acci<PERSON>', color: '#ef4444' },
          { id: 2, name: '<PERSON><PERSON>', color: '#8b5cf6' },
          { id: 3, name: '<PERSON><PERSON><PERSON> Principal', color: '#06b6d4' },
          { id: 4, name: 'Desarrollo', color: '#10b981' },
        ],
        settings: [
          { id: 1, name: 'project_name', value: 'Mi Novela' },
        ],
      };

      setCurrentProject(mockProject);
      setTags(mockProject.tags);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Crear nuevo proyecto
  const createProject = async (name: string, path: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar creación real con Electron
      console.log('Creando proyecto:', name, path);
      
      const newProject: Project = {
        name,
        path,
        documents: [],
        tags: [],
        settings: [
          { id: 1, name: 'project_name', value: name },
        ],
      };

      setCurrentProject(newProject);
      setDocuments([]);
      setTags([]);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Guardar proyecto
  const saveProject = async () => {
    if (!currentProject) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // TODO: Implementar guardado real
      console.log('Guardando proyecto:', currentProject.name);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  // Cargar proyecto por defecto al iniciar
  useEffect(() => {
    const initializeProject = () => {
      // Verificar si hay un proyecto guardado en localStorage
      const savedProject = localStorage.getItem('currentProject');
      if (savedProject) {
        try {
          const project = JSON.parse(savedProject);
          setCurrentProject(project);
          setTags(project.tags || []);
        } catch (err) {
          console.error('Error cargando proyecto guardado:', err);
          // Crear proyecto por defecto
          const defaultProject: Project = {
            name: 'Mi Novela',
            path: '/projects/mi-novela',
            documents: [],
            tags: [
              { id: 1, name: 'Acción', color: '#ef4444' },
              { id: 2, name: 'Misterio', color: '#8b5cf6' },
              { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
              { id: 4, name: 'Desarrollo', color: '#10b981' },
            ],
            settings: [
              { id: 1, name: 'project_name', value: 'Mi Novela' },
            ],
          };
          setCurrentProject(defaultProject);
          setTags(defaultProject.tags);
        }
      } else {
        // Crear proyecto por defecto
        const defaultProject: Project = {
          name: 'Mi Novela',
          path: '/projects/mi-novela',
          documents: [],
          tags: [
            { id: 1, name: 'Acción', color: '#ef4444' },
            { id: 2, name: 'Misterio', color: '#8b5cf6' },
            { id: 3, name: 'Personaje Principal', color: '#06b6d4' },
            { id: 4, name: 'Desarrollo', color: '#10b981' },
          ],
          settings: [
            { id: 1, name: 'project_name', value: 'Mi Novela' },
          ],
        };
        setCurrentProject(defaultProject);
        setTags(defaultProject.tags);
      }
    };

    initializeProject();
  }, []);

  // Guardar proyecto en localStorage cuando cambie
  useEffect(() => {
    if (currentProject) {
      localStorage.setItem('currentProject', JSON.stringify(currentProject));
    }
  }, [currentProject]);

  return {
    currentProject,
    documents,
    tags,
    isLoading,
    error,
    loadProject,
    createProject,
    saveProject,
  };
}
