import { useState, useEffect, useCallback } from 'react';
import { WorkspaceInstance, WorkspaceModeType, WorkspaceState } from '../types';

// Hook principal para gestionar múltiples instancias de workspace
export function useWorkspaces(documentId: number | null) {
  const [state, setState] = useState<WorkspaceState>({
    instances: [],
    activeInstanceId: null,
    documentId: documentId,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Clave para localStorage basada en el documento
  const getStorageKey = useCallback(() => {
    return documentId ? `workspaces_doc_${documentId}` : null;
  }, [documentId]);

  // Cargar instancias desde localStorage
  const loadInstances = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);

    try {
      const storageKey = getStorageKey();
      if (!storageKey) return;

      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const data = JSON.parse(stored);
        setState(prev => ({
          ...prev,
          instances: data.instances || [],
          activeInstanceId: data.activeInstanceId || null,
          documentId: documentId,
        }));
      } else {
        // Si no hay datos, crear instancia de editor por defecto
        const defaultEditorInstance: WorkspaceInstance = {
          id: `editor_${documentId}_${Date.now()}`,
          documentId: documentId,
          type: 'editor',
          name: 'Editor',
          data: {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        setState(prev => ({
          ...prev,
          instances: [defaultEditorInstance],
          activeInstanceId: defaultEditorInstance.id,
          documentId: documentId,
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error cargando workspaces');
      console.error('Error loading workspaces:', err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, getStorageKey]);

  // Guardar instancias en localStorage
  const saveInstances = useCallback(async (newState: WorkspaceState) => {
    if (!documentId) return;

    try {
      const storageKey = getStorageKey();
      if (!storageKey) return;

      const dataToSave = {
        instances: newState.instances,
        activeInstanceId: newState.activeInstanceId,
        lastUpdated: new Date().toISOString(),
      };

      localStorage.setItem(storageKey, JSON.stringify(dataToSave));
      console.log('Workspaces saved to localStorage');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error guardando workspaces');
      console.error('Error saving workspaces:', err);
    }
  }, [documentId, getStorageKey]);

  // Crear nueva instancia
  const createInstance = useCallback(async (
    type: WorkspaceModeType,
    name: string,
    initialData: any = {}
  ): Promise<WorkspaceInstance | null> => {
    if (!documentId) return null;

    setIsLoading(true);
    setError(null);

    try {
      // Validar nombre único
      const existingNames = state.instances.map(instance => instance.name.toLowerCase());
      if (existingNames.includes(name.toLowerCase())) {
        throw new Error(`Ya existe una instancia con el nombre "${name}"`);
      }

      const newInstance: WorkspaceInstance = {
        id: `${type}_${documentId}_${Date.now()}`,
        documentId: documentId,
        type: type,
        name: name,
        data: initialData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const newState = {
        ...state,
        instances: [...state.instances, newInstance],
        activeInstanceId: newInstance.id,
      };

      setState(newState);
      await saveInstances(newState);

      console.log('Created new workspace instance:', newInstance);
      return newInstance;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error creando instancia');
      console.error('Error creating instance:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [documentId, state, saveInstances]);

  // Eliminar instancia
  const deleteInstance = useCallback(async (instanceId: string): Promise<boolean> => {
    if (!documentId) return false;

    setIsLoading(true);
    setError(null);

    try {
      const instanceToDelete = state.instances.find(instance => instance.id === instanceId);
      if (!instanceToDelete) {
        throw new Error('Instancia no encontrada');
      }

      // No permitir eliminar la última instancia
      if (state.instances.length <= 1) {
        throw new Error('No se puede eliminar la última instancia');
      }

      const remainingInstances = state.instances.filter(instance => instance.id !== instanceId);
      
      // Si eliminamos la instancia activa, activar la primera disponible
      const newActiveId = state.activeInstanceId === instanceId 
        ? remainingInstances[0]?.id || null
        : state.activeInstanceId;

      const newState = {
        ...state,
        instances: remainingInstances,
        activeInstanceId: newActiveId,
      };

      setState(newState);
      await saveInstances(newState);

      console.log('Deleted workspace instance:', instanceId);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error eliminando instancia');
      console.error('Error deleting instance:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [documentId, state, saveInstances]);

  // Cambiar instancia activa
  const setActiveInstance = useCallback(async (instanceId: string | null): Promise<boolean> => {
    if (!documentId) return false;

    try {
      if (instanceId && !state.instances.find(instance => instance.id === instanceId)) {
        throw new Error('Instancia no encontrada');
      }

      const newState = {
        ...state,
        activeInstanceId: instanceId,
      };

      setState(newState);
      await saveInstances(newState);

      console.log('Set active instance:', instanceId);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error cambiando instancia activa');
      console.error('Error setting active instance:', err);
      return false;
    }
  }, [documentId, state, saveInstances]);

  // Actualizar datos de instancia
  const updateInstanceData = useCallback(async (
    instanceId: string,
    newData: any
  ): Promise<boolean> => {
    if (!documentId) return false;

    try {
      const updatedInstances = state.instances.map(instance => 
        instance.id === instanceId
          ? {
              ...instance,
              data: { ...instance.data, ...newData },
              updatedAt: new Date().toISOString(),
            }
          : instance
      );

      const newState = {
        ...state,
        instances: updatedInstances,
      };

      setState(newState);
      await saveInstances(newState);

      console.log('Updated instance data:', instanceId);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error actualizando datos');
      console.error('Error updating instance data:', err);
      return false;
    }
  }, [documentId, state, saveInstances]);

  // Renombrar instancia
  const renameInstance = useCallback(async (
    instanceId: string,
    newName: string
  ): Promise<boolean> => {
    if (!documentId) return false;

    try {
      // Validar nombre único (excluyendo la instancia actual)
      const existingNames = state.instances
        .filter(instance => instance.id !== instanceId)
        .map(instance => instance.name.toLowerCase());
      
      if (existingNames.includes(newName.toLowerCase())) {
        throw new Error(`Ya existe una instancia con el nombre "${newName}"`);
      }

      const updatedInstances = state.instances.map(instance => 
        instance.id === instanceId
          ? {
              ...instance,
              name: newName,
              updatedAt: new Date().toISOString(),
            }
          : instance
      );

      const newState = {
        ...state,
        instances: updatedInstances,
      };

      setState(newState);
      await saveInstances(newState);

      console.log('Renamed instance:', instanceId, 'to:', newName);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error renombrando instancia');
      console.error('Error renaming instance:', err);
      return false;
    }
  }, [documentId, state, saveInstances]);

  // Obtener instancia activa
  const getActiveInstance = useCallback((): WorkspaceInstance | null => {
    if (!state.activeInstanceId) return null;
    return state.instances.find(instance => instance.id === state.activeInstanceId) || null;
  }, [state.activeInstanceId, state.instances]);

  // Obtener instancias por tipo
  const getInstancesByType = useCallback((type: WorkspaceModeType): WorkspaceInstance[] => {
    return state.instances.filter(instance => instance.type === type);
  }, [state.instances]);

  // Cargar instancias cuando cambia el documento
  useEffect(() => {
    if (documentId) {
      loadInstances();
    } else {
      setState({
        instances: [],
        activeInstanceId: null,
        documentId: null,
      });
    }
  }, [documentId, loadInstances]);

  return {
    // Estado
    instances: state.instances,
    activeInstanceId: state.activeInstanceId,
    activeInstance: getActiveInstance(),
    isLoading,
    error,

    // Acciones
    createInstance,
    deleteInstance,
    setActiveInstance,
    updateInstanceData,
    renameInstance,
    
    // Utilidades
    getInstancesByType,
    loadInstances,
  };
}
