import { useState, useEffect, useCallback } from 'react';
import { WorkspaceInstance, CorkboardData, CorkboardCard } from '../types';

// Hook especializado para manejar la lógica del corkboard
export function useCorkboard(
  instance: WorkspaceInstance,
  onUpdateData: (data: any) => void
) {
  const [cards, setCards] = useState<CorkboardCard[]>([]);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);

  // Cargar datos del corkboard desde la instancia
  useEffect(() => {
    const corkboardData = instance.data as CorkboardData;
    if (corkboardData && corkboardData.cards) {
      setCards(corkboardData.cards);
    } else {
      // Inicializar con datos vacíos
      setCards([]);
    }
  }, [instance]);

  // Función para actualizar los datos de la instancia
  const updateInstanceData = useCallback((newCards: CorkboardCard[]) => {
    const corkboardData: CorkboardData = {
      cards: newCards,
      background: instance.data?.background || '#f8fafc',
      zoom: instance.data?.zoom || 1,
    };
    
    onUpdateData(corkboardData);
  }, [instance.data, onUpdateData]);

  // Agregar nueva tarjeta
  const addCard = useCallback((cardData: Partial<CorkboardCard>) => {
    const newCard: CorkboardCard = {
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      x: cardData.x || 100,
      y: cardData.y || 100,
      width: cardData.width || 200,
      height: cardData.height || 150,
      color: cardData.color || '#fbbf24',
      content: cardData.content || 'Nueva tarjeta',
      documentId: cardData.documentId,
      imageUrl: cardData.imageUrl,
    };

    const newCards = [...cards, newCard];
    setCards(newCards);
    updateInstanceData(newCards);
    
    // Seleccionar la nueva tarjeta
    setSelectedCards([newCard.id]);
    
    console.log('Added new card:', newCard);
  }, [cards, updateInstanceData]);

  // Actualizar tarjeta existente
  const updateCard = useCallback((cardId: string, updates: Partial<CorkboardCard>) => {
    const newCards = cards.map(card => 
      card.id === cardId 
        ? { ...card, ...updates }
        : card
    );
    
    setCards(newCards);
    updateInstanceData(newCards);
    
    console.log('Updated card:', cardId, updates);
  }, [cards, updateInstanceData]);

  // Eliminar tarjeta
  const deleteCard = useCallback((cardId: string) => {
    const newCards = cards.filter(card => card.id !== cardId);
    setCards(newCards);
    updateInstanceData(newCards);
    
    // Remover de selección si estaba seleccionada
    setSelectedCards(prev => prev.filter(id => id !== cardId));
    
    console.log('Deleted card:', cardId);
  }, [cards, updateInstanceData]);

  // Seleccionar tarjeta
  const selectCard = useCallback((cardId: string, multiSelect: boolean = false) => {
    if (multiSelect) {
      setSelectedCards(prev => 
        prev.includes(cardId)
          ? prev.filter(id => id !== cardId)
          : [...prev, cardId]
      );
    } else {
      setSelectedCards([cardId]);
    }
  }, []);

  // Limpiar selección
  const clearSelection = useCallback(() => {
    setSelectedCards([]);
  }, []);

  // Mover tarjeta
  const moveCard = useCallback((cardId: string, deltaX: number, deltaY: number) => {
    const newCards = cards.map(card => 
      card.id === cardId 
        ? { 
            ...card, 
            x: card.x + deltaX,
            y: card.y + deltaY
          }
        : card
    );
    
    setCards(newCards);
    updateInstanceData(newCards);
  }, [cards, updateInstanceData]);

  // Mover múltiples tarjetas seleccionadas
  const moveSelectedCards = useCallback((deltaX: number, deltaY: number) => {
    const newCards = cards.map(card => 
      selectedCards.includes(card.id)
        ? { 
            ...card, 
            x: card.x + deltaX,
            y: card.y + deltaY
          }
        : card
    );
    
    setCards(newCards);
    updateInstanceData(newCards);
  }, [cards, selectedCards, updateInstanceData]);

  // Duplicar tarjeta
  const duplicateCard = useCallback((cardId: string) => {
    const originalCard = cards.find(card => card.id === cardId);
    if (!originalCard) return;

    const duplicatedCard: CorkboardCard = {
      ...originalCard,
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      x: originalCard.x + 20,
      y: originalCard.y + 20,
      content: `${originalCard.content} (copia)`,
    };

    const newCards = [...cards, duplicatedCard];
    setCards(newCards);
    updateInstanceData(newCards);
    
    // Seleccionar la tarjeta duplicada
    setSelectedCards([duplicatedCard.id]);
    
    console.log('Duplicated card:', duplicatedCard);
  }, [cards, updateInstanceData]);

  // Cambiar color de tarjetas seleccionadas
  const changeSelectedCardsColor = useCallback((color: string) => {
    const newCards = cards.map(card => 
      selectedCards.includes(card.id)
        ? { ...card, color }
        : card
    );
    
    setCards(newCards);
    updateInstanceData(newCards);
  }, [cards, selectedCards, updateInstanceData]);

  // Eliminar tarjetas seleccionadas
  const deleteSelectedCards = useCallback(() => {
    const newCards = cards.filter(card => !selectedCards.includes(card.id));
    setCards(newCards);
    updateInstanceData(newCards);
    setSelectedCards([]);
    
    console.log('Deleted selected cards:', selectedCards);
  }, [cards, selectedCards, updateInstanceData]);

  // Obtener tarjeta por ID
  const getCardById = useCallback((cardId: string): CorkboardCard | undefined => {
    return cards.find(card => card.id === cardId);
  }, [cards]);

  // Obtener estadísticas del corkboard
  const getStats = useCallback(() => {
    return {
      totalCards: cards.length,
      selectedCards: selectedCards.length,
      colors: [...new Set(cards.map(card => card.color))],
      hasImages: cards.some(card => card.imageUrl),
      linkedDocuments: cards.filter(card => card.documentId).length,
    };
  }, [cards, selectedCards]);

  // Crear tarjeta desde template
  const createCardFromTemplate = useCallback((template: 'character' | 'location' | 'idea' | 'note') => {
    const templates = {
      character: {
        content: '👤 Nombre del Personaje\n\nDescripción...',
        color: '#3b82f6',
        width: 200,
        height: 180,
      },
      location: {
        content: '🏛️ Nombre del Lugar\n\nDescripción...',
        color: '#10b981',
        width: 200,
        height: 180,
      },
      idea: {
        content: '💡 Idea\n\nDetalles...',
        color: '#fbbf24',
        width: 180,
        height: 150,
      },
      note: {
        content: '📝 Nota\n\nContenido...',
        color: '#ec4899',
        width: 180,
        height: 150,
      },
    };

    const templateData = templates[template];
    addCard({
      ...templateData,
      x: 100 + Math.random() * 200,
      y: 100 + Math.random() * 200,
    });
  }, [addCard]);

  // Alinear tarjetas seleccionadas
  const alignSelectedCards = useCallback((alignment: 'left' | 'center' | 'grid') => {
    if (selectedCards.length === 0) return;

    const selectedCardObjects = cards.filter(card => selectedCards.includes(card.id));

    let newCards = [...cards];

    switch (alignment) {
      case 'left':
        // Alinear todas las tarjetas seleccionadas a la izquierda de la primera
        const leftmostX = Math.min(...selectedCardObjects.map(card => card.x));
        newCards = cards.map(card =>
          selectedCards.includes(card.id)
            ? { ...card, x: leftmostX }
            : card
        );
        break;

      case 'center':
        // Centrar todas las tarjetas seleccionadas
        const centerX = selectedCardObjects.reduce((sum, card) => sum + card.x, 0) / selectedCardObjects.length;
        newCards = cards.map(card =>
          selectedCards.includes(card.id)
            ? { ...card, x: centerX - card.width / 2 }
            : card
        );
        break;

      case 'grid':
        // Organizar en cuadrícula
        const gridSize = Math.ceil(Math.sqrt(selectedCardObjects.length));
        const spacing = 220; // Espacio entre tarjetas
        const startX = Math.min(...selectedCardObjects.map(card => card.x));
        const startY = Math.min(...selectedCardObjects.map(card => card.y));

        newCards = cards.map(card => {
          const index = selectedCards.indexOf(card.id);
          if (index === -1) return card;

          const row = Math.floor(index / gridSize);
          const col = index % gridSize;

          return {
            ...card,
            x: startX + col * spacing,
            y: startY + row * spacing,
          };
        });
        break;
    }

    setCards(newCards);
    updateInstanceData(newCards);
  }, [cards, selectedCards, updateInstanceData]);

  // Duplicar tarjetas seleccionadas
  const duplicateSelectedCards = useCallback(() => {
    if (selectedCards.length === 0) return;

    const selectedCardObjects = cards.filter(card => selectedCards.includes(card.id));
    const duplicatedCards = selectedCardObjects.map(card => ({
      ...card,
      id: `card_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      x: card.x + 20,
      y: card.y + 20,
      content: `${card.content} (copia)`,
    }));

    const newCards = [...cards, ...duplicatedCards];
    setCards(newCards);
    updateInstanceData(newCards);

    // Seleccionar las tarjetas duplicadas
    setSelectedCards(duplicatedCards.map(card => card.id));
  }, [cards, selectedCards, updateInstanceData]);

  return {
    // Estado
    cards,
    selectedCards,
    
    // Acciones básicas
    addCard,
    updateCard,
    deleteCard,
    
    // Selección
    selectCard,
    clearSelection,
    
    // Movimiento
    moveCard,
    moveSelectedCards,
    
    // Acciones avanzadas
    duplicateCard,
    duplicateSelectedCards,
    changeSelectedCardsColor,
    deleteSelectedCards,
    createCardFromTemplate,
    alignSelectedCards,

    // Utilidades
    getCardById,
    getStats,
  };
}
