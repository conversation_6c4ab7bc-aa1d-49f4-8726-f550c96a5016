// Tipos principales para el proyecto Gutenberg Writer

export interface Document {
  id: number;
  title: string;
  content?: string;
  synopsis?: string;
  order_index: number;
  parent_id?: number;
  is_folder: boolean;
  created_at: string;
  updated_at: string;
  include_in_compile: boolean;
  compile_order?: number;
  compile_content: boolean;
  compile_title: boolean;
  compile_synopsis: boolean;
  children?: Document[];
}

export interface DocumentImage {
  id: number;
  document_id: number;
  path: string;
  caption?: string;
  order_index: number;
  include_in_compile: boolean;
}

export interface Tag {
  id: number;
  name: string;
  color: string;
}

export interface DocumentTag {
  document_id: number;
  tag_id: number;
}

export interface ProjectSettings {
  id: number;
  name: string;
  value: string;
}

export interface Project {
  name: string;
  path: string;
  documents: Document[];
  tags: Tag[];
  settings: ProjectSettings[];
}

// Tipos para la interfaz de usuario
export interface DocumentTreeNode extends Document {
  level: number;
  isExpanded?: boolean;
  hasChildren: boolean;
}

export interface CompileProfile {
  id: string;
  name: string;
  includeTitle: boolean;
  includeSynopsis: boolean;
  includeContent: boolean;
  format: 'pdf' | 'markdown' | 'docx' | 'epub';
  documentSelection: number[];
}

// Tipos para el editor
export interface EditorState {
  content: string;
  wordCount: number;
  characterCount: number;
  isModified: boolean;
}

// Tipos para eventos
export interface DocumentSelectEvent {
  documentId: number;
  document: Document;
}

export interface DocumentUpdateEvent {
  documentId: number;
  field: keyof Document;
  value: any;
}

export interface TagUpdateEvent {
  tagId: number;
  field: keyof Tag;
  value: any;
}
