// Tipos principales para el proyecto Gutenberg Writer

export interface Document {
  id: number;
  title: string;
  content?: string;
  synopsis?: string;
  order_index: number;
  parent_id?: number;
  is_folder: boolean;
  created_at: string;
  updated_at: string;
  include_in_compile: boolean;
  compile_order?: number;
  compile_content: boolean;
  compile_title: boolean;
  compile_synopsis: boolean;
  children?: Document[];
}

export interface DocumentImage {
  id: number;
  document_id: number;
  path: string;
  caption?: string;
  order_index: number;
  include_in_compile: boolean;
}

export interface Tag {
  id: number;
  name: string;
  color: string;
}

export interface DocumentTag {
  document_id: number;
  tag_id: number;
}

export interface ProjectSettings {
  id: number;
  name: string;
  value: string;
}

export interface Project {
  name: string;
  path: string;
  documents: Document[];
  tags: Tag[];
  settings: ProjectSettings[];
}

// Tipos para la interfaz de usuario
export interface DocumentTreeNode extends Document {
  level: number;
  isExpanded?: boolean;
  hasChildren: boolean;
}

export interface CompileProfile {
  id: string;
  name: string;
  includeTitle: boolean;
  includeSynopsis: boolean;
  includeContent: boolean;
  format: 'pdf' | 'markdown' | 'docx' | 'epub';
  documentSelection: number[];
}

// Tipos para el editor
export interface EditorState {
  content: string;
  wordCount: number;
  characterCount: number;
  isModified: boolean;
}

// Tipos para el sistema de workspace con múltiples instancias
export type WorkspaceModeType =
  | 'editor'
  | 'corkboard'
  | 'canvas'
  | 'timeline'
  | 'outline'
  | 'character'
  | 'world';

export interface WorkspaceInstance {
  id: string;
  documentId: number;
  type: WorkspaceModeType;
  name: string;
  data: any; // Datos específicos del workspace (tarjetas, elementos canvas, etc.)
  createdAt: string;
  updatedAt: string;
}

export interface WorkspaceModeConfig {
  type: WorkspaceModeType;
  name: string;
  icon: string;
  allowMultipleInstances: boolean;
  defaultName: string;
  maxInstances?: number;
  templates?: WorkspaceTemplate[];
}

export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  defaultData: any;
}

export interface WorkspaceState {
  instances: WorkspaceInstance[];
  activeInstanceId: string | null;
  documentId: number | null;
}

// Tipos específicos para cada modo de workspace
export interface CorkboardData {
  cards: CorkboardCard[];
  background?: string;
  zoom?: number;
}

export interface CorkboardCard {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  color: string;
  content: string;
  documentId?: number;
  imageUrl?: string;
}

export interface CanvasData {
  elements: CanvasElement[];
  background?: string;
  zoom?: number;
  pan?: { x: number; y: number };
}

export interface CanvasElement {
  id: string;
  type: 'rectangle' | 'circle' | 'line' | 'text' | 'image';
  x: number;
  y: number;
  width?: number;
  height?: number;
  color?: string;
  text?: string;
  imageUrl?: string;
  connections?: string[]; // IDs de elementos conectados
}

export interface TimelineData {
  events: TimelineEvent[];
  startDate: string;
  endDate: string;
  zoom?: number;
  viewType?: 'days' | 'months' | 'years';
}

export interface TimelineEvent {
  id: string;
  title: string;
  description?: string;
  date: string;
  endDate?: string; // Para eventos con duración
  color?: string;
  documentId?: number;
  category?: string;
}

// Tipos para eventos
export interface DocumentSelectEvent {
  documentId: number;
  document: Document;
}

export interface DocumentUpdateEvent {
  documentId: number;
  field: keyof Document;
  value: any;
}

export interface TagUpdateEvent {
  tagId: number;
  field: keyof Tag;
  value: any;
}
